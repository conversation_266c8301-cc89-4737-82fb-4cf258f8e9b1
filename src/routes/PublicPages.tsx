import ErrorBoundaryAndSuspense from '../common/components/ErrorBoundaryAndSuspense'
import { isLite } from '../config/lite'
import LoginForm from '../features/authentication/pages/LoginForm'
import PinResetPage from '../features/authentication/pages/PinResetPage'
import RegisterPage from '../features/authentication/pages/RegisterPage'
import Home from '../features/dashboard/components/Home'
//MyTontine lite
import CalculatorForms from '../features/dashboard/pages/CalculatorForms'
import PreRegisterForecast from '../features/dashboard/pages/PreRegisterForecast'
import PublicStatsPage from '../features/referral/pages/PublicStatsPage'
import { PRIVATE, PUBLIC } from './Route'

const MyTontinePages = isLite
  ? [
      //MyTontine Lite only routes
      {
        // Default page
        path: PUBLIC.HOME,
        page: <PreRegisterForecast />,
      },
      {
        // Allows for the email to be verified on the homepage as well
        path: PUBLIC.VERIFY,
        page: <PreRegisterForecast />,
      },
      {
        // It is private because it needs auth token to get results
        // from the API  looks like a contradiction, but these are all the pages
        // in MTL, so no need to add them to private
        path: PRIVATE.WEBSITE_REWARDS,
        page: <PublicStatsPage />,
      },
      {
        path: PUBLIC.NOT_FOUND_404,
        page: <PreRegisterForecast />,
      },
    ]
  : // MY TONTINE FULL APP BELOW
    [
      {
        path: PUBLIC.PIN_RESET,
        page: <PinResetPage />,
      },
      {
        path: PUBLIC.SIGN_IN,
        page: (
          <ErrorBoundaryAndSuspense>
            <LoginForm />
          </ErrorBoundaryAndSuspense>
        ),
      },
      {
        path: PUBLIC.SIGN_UP,
        page: <RegisterPage />,
      },
      {
        path: PUBLIC.SIGN_UP_REFERRAL,
        page: <RegisterPage />,
      },
      {
        path: PUBLIC.TONTINATOR,
        page: <CalculatorForms />,
      },
      {
        path: PUBLIC.HOME,
        page: <Home />,
      },
      {
        path: PUBLIC.NOT_FOUND_404,
        page: <Home />,
      },
    ]

/**
 * Accessed only by anon users
 * These pages are going to be included in the MyTontine Lite
 */
const PublicPages = [...MyTontinePages] as const

export { PublicPages }
