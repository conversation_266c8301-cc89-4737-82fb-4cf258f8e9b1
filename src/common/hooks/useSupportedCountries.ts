import { useMemo } from 'react'
import { useBankingService } from '../../features/banking/hooks/useBankingService'
import {
  AgeMonth,
  ForecastRules,
  TontineProductRules,
  TontineProducts,
  YearMonth,
} from '../../features/banking/types/BankMachineTypes.type'
import {
  getCountryInformation,
  getSupportedTontinatorParams,
} from '../utils/UtilFunctions'

type ParsedProductRules = {
  minRetirementAge: AgeMonth
  minRetirementAgeYearMonth: YearMonth
  maxRetirementAge: AgeMonth
  maxRetirementAgeYearMonth: YearMonth
  minCurrentAge: AgeMonth
  minCurrentAgeYearMonth: YearMonth
  maxCurrentAge: AgeMonth
  maxCurrentAgeYearMonth: YearMonth
}

/**
 * Un-nests nested objects about product rules into a nice flat object
 */
const parseRuleValue = (
  product: TontineProductRules
): Record<TontineProducts, ParsedProductRules> => {
  if (product) {
    return Object.entries(product).reduce(
      (
        acc: Record<TontineProducts, ParsedProductRules>,
        [productKey, productRules]
      ) => {
        const { contribution_min, payout_max, payout_min } = productRules

        acc[productKey as TontineProducts] = {
          minRetirementAge: payout_min.age_month,
          minRetirementAgeYearMonth: payout_min.year_month,
          maxRetirementAge: payout_max.age_month,
          maxRetirementAgeYearMonth: payout_max.year_month,
          minCurrentAge: contribution_min.age_month,
          minCurrentAgeYearMonth: contribution_min.year_month,
          // There is no max object for the contribution age it is just the max
          // payout age because they are the same
          maxCurrentAge: payout_max.age_month,
          maxCurrentAgeYearMonth: payout_max.year_month,
        }

        return acc
      },
      {} as Record<TontineProducts, ParsedProductRules>
    )
  }

  throw new Error(
    `Invalid product key: ${product as string}, valid keys are: ${Object.keys(
      product
    ).join(', ')}`
  )
}

/**
 * Returns appropriate rules for making forecasts within the max and min limits
 */
const findForecastRulesForCountry = ({
  alpha3CountryCode,
  forecastRules,
}: {
  alpha3CountryCode: string
  forecastRules: ForecastRules
}) => {
  // Some regions are countries itself, so we can get a quick win to return the
  // necessary country easily
  let foundCountry = forecastRules?.[alpha3CountryCode]

  if (!foundCountry) {
    // No country found look for regions
    Object.keys(forecastRules).forEach((region) => {
      // Look up the countries for the region
      forecastRules?.[region]?.countries.find((country) => {
        if (country === alpha3CountryCode) {
          // if country matches he country in the region
          // init the rules for that country
          foundCountry = forecastRules?.[region]
        }
        // .find to stop needs this
        return country === alpha3CountryCode
      })
    })
  }

  if (foundCountry) {
    return {
      currency: foundCountry?.currency,
      products: parseRuleValue(foundCountry.products),
      supportedInvestments: foundCountry?.supported_investments,
    }
  }

  return {}
}

/**
 * Returns memorized supported country information with default tontinator
 * params and limits from the backend.
 *
 * The tontine product uses state
 */
export const useSupportedCountries = ({
  alpha3CountryCode,
  onCountryChanged,
}: {
  alpha3CountryCode?: string
  onCountryChanged?: (supportedCountry: object) => void
}) => {
  const { bankContext } = useBankingService()

  const supportedCountry = useMemo(() => {
    let country = getCountryInformation('alpha3', alpha3CountryCode)

    if (!country) {
      // If country that is not in the json list
      // default to Ireland
      country = getCountryInformation('alpha3', 'USA')
    }

    const limitsFromBackend = findForecastRulesForCountry({
      alpha3CountryCode: country?.alpha3 ?? 'USA',
      forecastRules: bankContext?.returns?.forecastRules ?? {},
    })

    const uiDefaultValues = getSupportedTontinatorParams(
      country?.alpha3 ?? 'USA'
    )

    const tontineProduct = bankContext?.tontineProduct

    const tontinatorParams = {
      ...uiDefaultValues,
      ...limitsFromBackend?.products?.[tontineProduct],
    }

    const countryInfo = {
      ...country,
      currency: limitsFromBackend?.currency,
      supportedInvestments: limitsFromBackend?.supportedInvestments,
      //TODO: Quick fix for dynamic currency
      currencySymbol: country?.currency === 'USD' ? '$' : '€',
      tontinatorParams,
    }

    onCountryChanged?.(countryInfo)

    return countryInfo
  }, [
    alpha3CountryCode,
    onCountryChanged,
    bankContext?.tontineProduct,
    bankContext?.returns?.forecastRules,
  ])

  return {
    supportedCountry,
  }
}
