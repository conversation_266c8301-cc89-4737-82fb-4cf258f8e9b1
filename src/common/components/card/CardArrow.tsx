import { ASSET } from '../../constants/Assets'
import { UI_TEST_ID } from '../../constants/DataTestIDs'
import style from '../../style/Card.module.scss'
import { CardArrowProps } from '../../types/CardArrow.types'
import Icon from '../Icon'

/**
 * CardArrow component renders an arrow icon with various styles based on the provided props.
 */
const CardArrow = ({
  variant,
  rotateArrow,
  arrowInvisible,
  secondaryIcon,
}: CardArrowProps) => {
  const arrowClasses = [
    style[`card__arrow`],
    !arrowInvisible &&
      variant &&
      style[`card__arrow${variant ? `--${variant}` : ''}`],
    !arrowInvisible &&
      rotateArrow &&
      style[`card__arrow${rotateArrow ? `--${rotateArrow}` : ''}`],
    arrowInvisible && style[`card__arrow--hidden`],
  ]
    .filter(Boolean)
    .join(' ')
  return (
    <Icon
      dataTestId={UI_TEST_ID?.cardArrow}
      className={arrowClasses}
      fileName={secondaryIcon ?? ASSET.icononboardinarrowforward}
    />
  )
}

export default CardArrow
