import { ChangeEvent, useEffect, useRef } from 'react'
import { track } from '../analytics/Analytics'
import { SliderEvent } from '../analytics/EventData'
import style from '../style/SliderInput.module.scss'
import { RangeProps } from '../types/Range.types'
import { selectDomElement } from '../utils/UtilFunctions'

const variantToColor = {
  yellow: '#ffcc50',
  blue: '#7ebaf7',
}

const disabledAreaColor = '#dddd'

/**
 * Finds the index of a given value in an array of steps that are passed in for
 * the range input
 */
const findStepIndex = (steps: Array<number>, value: number) =>
  steps?.findIndex((step) => step === value)

/**
 * Renders a draggable slider using the `input` default html5
 * component
 */
const Range = ({
  onChange,
  steps = [],
  value = 0,
  disabled,
  thumbBubble,
  className,
  variant,
  trackRangeId,
  disabledIncrement,
  disabledDecrement,
  enabledSteps,
}: RangeProps) => {
  const trackMovement = ({ objectValue }: { objectValue: number }) => {
    void track({
      event: SliderEvent.moved,
      properties: {
        object_id: trackRangeId,
        object_value: objectValue,
      },
    })
  }

  const instanceId = Array.from({ length: 7 }, () =>
    String.fromCharCode(65 + Math.floor(Math.random() * 26))
  ).join('')
  const instanceIdTip = `${instanceId}tip`
  const bubbleId = `${instanceId}bubble`
  const sliderRef = useRef<HTMLInputElement | null>(null)
  const color = variantToColor[variant ?? 'blue']

  /**
   * Moves the bubble that is above the slider thumb, it follow the slider thumb
   * and adjusts the bubble accordingly so it does not go past the Range.jsx's
   * width
   */
  const moveThumbBubble = (steps: Array<number>, value: number) => {
    // Select the slider thumb bubble and the static tooltip elements
    const tooltip = selectDomElement(`.${bubbleId}`)
    const tip = selectDomElement(`.${instanceIdTip}`)

    if (tooltip && tip) {
      // Set the position of the tooltip to be absolute and above the slider thumb
      // bubble
      tip.style.position = 'absolute'
      tip.style.top = '-20px'
      // 2.5px
      const initialTipPosition = 2.5

      if (sliderRef.current) {
        // Get the width of the slider and the slider thumb bubble
        const sliderWidth = sliderRef.current.getBoundingClientRect().width
        const tooltipWidth = tooltip.getBoundingClientRect().width
        const thumbWidth = 31

        // Find the index of the current value in the steps array and calculate its percentage position
        const currentIndex = findStepIndex(steps, value)
        const percent = currentIndex / (steps.length - 1)
        // Slider width scaled with the width of the thumb
        const scaledSliderWidth = Math.floor(sliderWidth * percent)
        const scaledThumbWidth = Math.floor(thumbWidth * percent)

        // Calculate the position of the slider thumb bubble based on the percentage position
        const thumbPosition =
          (percent * 100 * (sliderWidth - tooltipWidth)) / 100
        tooltip.style.left = `${thumbPosition}px`
        //Positions the tip exactly on top and middle of the thumb
        const currentTipPosition = scaledSliderWidth - scaledThumbWidth
        tip.style.left = `${currentTipPosition > 0 ? currentTipPosition : initialTipPosition}px`
      }
    }
  }

  //After every render adjust the gradient on the slider.
  useEffect(() => {
    let areaBefore = 0
    let areaAfter = 100
    const cutTinyPixel = disabled ? 2 : 0

    if (enabledSteps && enabledSteps?.length > 0) {
      // Find start and end index of the enabled steps
      const startIndex = steps.indexOf(enabledSteps?.[0])
      const endIndex = steps.indexOf(enabledSteps?.[enabledSteps?.length - 1])
      // Create a sub array from steps disabled from left side
      const elementsBeforeEnabledSteps = steps.slice(0, startIndex)
      areaBefore =
        (elementsBeforeEnabledSteps?.length / steps?.length) * 100 +
        cutTinyPixel
      // Calculate area after ENABLED range
      const elementsAfterEnabledSteps = steps.slice(endIndex, steps?.length - 1)
      areaAfter = Math.ceil(
        (1 - elementsAfterEnabledSteps?.length / steps?.length) * 100
      )
    }
    if (sliderRef.current) {
      sliderRef.current.style.backgroundImage = `linear-gradient(to right, ${disabledAreaColor} 0 ${areaBefore}%, 
                                                 ${color} ${areaBefore}% ${areaAfter}%, 
                                                 ${disabledAreaColor} ${areaAfter}% 100%)`
    }
  }, [enabledSteps?.length, steps?.length, color])

  useEffect(() => {
    if (thumbBubble) {
      moveThumbBubble(steps, value)
    }
  })

  const handleInput = (event: ChangeEvent<HTMLInputElement>) => {
    const sliderValue = steps[Number(event.target.value)]

    if (thumbBubble) {
      moveThumbBubble(steps, sliderValue)
    }
    onChange?.(sliderValue)

    // Tracking needs to be delayed because the
    // value handling min,max is handled in the state
    // and not in the input
    // otherwise if the user is very fast enough they can send
    // retirement age 18 because the value handling is in the state and not here
    const trackTimer: NodeJS.Timeout | undefined = undefined

    if (!disabledDecrement && !disabledIncrement) {
      clearTimeout(trackTimer)
      setTimeout(() => {
        trackMovement({ objectValue: sliderValue })
      }, 500)
    }
  }

  //NOTE: the input uses the INDICES of the steps, not the values.
  return (
    <div className={`${style[`sliderInput__range-container`]} ${className}`}>
      <input
        ref={sliderRef}
        className={`${style[`sliderInput__range${disabled ? '--disabled' : ''}`]} ${style[`sliderInput__range${variant ? `--${variant}` : ''}`]} `}
        max={steps?.length - 1}
        min={0}
        onChange={handleInput}
        step={1}
        type="range"
        value={findStepIndex(steps, value)}
        disabled={disabled}
      />
      {thumbBubble && (
        <div className={`${style[`sliderInput__bubble`]} ${bubbleId}`}>
          {thumbBubble}
        </div>
      )}
      {thumbBubble && (
        <div className={`${style['sliderInput__tip']} ${instanceIdTip}`} />
      )}
    </div>
  )
}

export default Range
