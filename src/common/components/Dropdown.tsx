import { cloneElement, ReactElement, useEffect, useRef, useState } from 'react'
import { ASSET } from '../constants/Assets'
import { useDetectOutsideClick } from '../hooks/useDetectOutsideClick'
import { useSearch } from '../hooks/useSearch'
import { useTranslate } from '../hooks/useTranslate'
import style from '../style/Dropdown.module.scss'
import { DropdownProps, ItemKey, OptionValue } from '../types/Dropdown.types'
import DropdownMenu from './DropdownMenu'
import DropdownMenuItems from './DropdownMenuItems'
import OptionView from './OptionView'
import TextInput from './TextInput'

/**
 * @note `searchBy` only supports searching in flat object `options`
 *
 * Renders a searchable dropdown component that takes in json options
 */
const Dropdown = <T,>({
  options,
  itemKey,
  value,
  onChange,
  label,
  searchBy,
  readOnly,
  className,
  inputHeight,
  errorMessage,
  validatorFunction,
  placeholder,
  optional,
  alternateLabel,
  dataTestID,
  onSearchQueryTyped,
}: DropdownProps<T>) => {
  const t = useTranslate()

  const { foundOptions, searchQuery, setSearchQuery } = useSearch({
    options,
    searchBy,
  })

  const { setSelectedOption, selectedOption } = useRenderVisualOption({
    options,
    value,
    itemKey,
  })

  const { mainContainerRef, isOpenMenu, setIsOpenMenu } =
    useCloseOnClickOutside(() => {
      setIsOpenMenu(false)
      // Resets the research query when the menu is closed
      setSearchQuery('')
    })

  /**
   * Handles the click event on an option and issues the onChange event
   */
  const handleOptionClick = (option: T) => {
    if (itemKey?.valueOnChange) {
      // this returns a string based on the itemKey
      onChange((option as OptionValue)[itemKey?.valueOnChange] as T)
    } else {
      onChange(option)
    }

    /**
     * Extracts a value from the option object using the `displayKey`
     */
    const renderText = (option: unknown): string => {
      return (option as OptionValue)[itemKey.displayKey]
    }

    // Clear the query so the dropdown menu resets resets
    setSearchQuery('')

    // Set the Input to have the selected option as a component
    setSelectedOption(
      <OptionView
        icon={(option as { icon?: string })?.icon}
        text={renderText(option)}
        secondaryIcon={(option as { secondaryIcon?: string })?.secondaryIcon}
      />
    )
    setIsOpenMenu(false)
  }

  return (
    <article
      className={`${style['dropdown']} ${className ?? ''}`}
      ref={mainContainerRef}
    >
      <TextInput
        // Prevents the jsx overlapping with a normal input placeholder
        placeholder={selectedOption ? '' : placeholder}
        jsxValue={applyOpacity({
          // Adds opacity to the selected element when dropdown menu is open,
          // looks cool
          selectedOption,
          isOpenMenu,
        })}
        label={label}
        alternateLabel={alternateLabel}
        value={searchQuery}
        onChange={(value: string) => {
          setSearchQuery(value)
          onSearchQueryTyped?.(value)
        }}
        inputMode="search"
        readOnly={readOnly}
        onClick={
          // Clicking the search input will only open the dropdown menu
          readOnly ? undefined : () => setIsOpenMenu(true)
        }
        onClickSuffixIcon={() => setIsOpenMenu((prev) => !prev)}
        suffixIcon={chevronPosition({
          readOnly,
          isOpenMenu,
        })}
        height={inputHeight}
        errorMessage={errorMessage}
        validatorFunction={validatorFunction}
        optional={optional}
        dataTestID={dataTestID}
      />

      <DropdownMenu isOpen={isOpenMenu}>
        <DropdownMenuItems
          handleOptionClick={handleOptionClick as (value: unknown) => void}
          options={foundOptions as unknown[]}
          itemKey={itemKey}
          value={value}
          noOptionFoundMessage={t('NO.DATA.FOR.THAT')}
          block={style['dropdown']}
        />
      </DropdownMenu>
    </article>
  )
}

/**
 * Renders a chevron position if up if menu is open, down if it is closed and
 * does not render a chevron if `readOnly`
 */
const chevronPosition = ({
  readOnly,
  isOpenMenu,
}: {
  readOnly?: boolean
  isOpenMenu: boolean
}): string => {
  if (readOnly) {
    return ''
  }

  return isOpenMenu ? ASSET.icononboardinarrowup : ASSET.icononboardinarrowdown
}

/**
 * Renders an option JSX component for passed in value so an Icon and other jsx
 * elements can be displayed inside the input component
 */
const useRenderVisualOption = <T,>({
  value,
  itemKey,
  options,
}: {
  options: Array<T>
  value: T
  itemKey: ItemKey
}) => {
  /**
   * Initializes the selected option with a jsx value based on the passed in
   * value for the dropdown
   */
  const initSelectedOptionIfValue = (
    value: T,
    itemKey: ItemKey,
    options: Array<T>
  ): ReactElement<{ className: string }> | undefined => {
    // Find the the the options from the Array of options
    let foundOption = options?.find((option) => {
      if (value && typeof value === 'string' && itemKey?.valueOnChange) {
        return option
          ? value === (option as OptionValue)[itemKey.valueOnChange]
          : undefined
      }

      return undefined
    }) as OptionValue | undefined

    // If value is an object don't search for anything, just return it to init
    // the state
    if (!foundOption) {
      foundOption = value as OptionValue
    }

    if (foundOption) {
      return (
        <OptionView
          icon={foundOption?.icon}
          text={foundOption[itemKey.displayKey]}
          secondaryIcon={foundOption?.secondaryIcon}
        />
      )
    }

    return undefined
  }

  const [selectedOption, setSelectedOption] = useState<
    ReactElement<{ className: string }> | undefined
  >(() => initSelectedOptionIfValue(value, itemKey, options))
  useEffect(() => {
    if (!value) {
      setSelectedOption(undefined)
    } else {
      // Fixes stale state init
      setSelectedOption(initSelectedOptionIfValue(value, itemKey, options))
    }
  }, [value, options])

  return {
    selectedOption,
    setSelectedOption,
  }
}

/**
 * Controls the opening and closing of the dropdown menu
 */
const useCloseOnClickOutside = (onClickedOutsideArea: () => void) => {
  const [isOpenMenu, setIsOpenMenu] = useState(false)

  const mainContainerRef = useRef<HTMLElement>(null)

  useDetectOutsideClick(mainContainerRef, onClickedOutsideArea)

  return {
    mainContainerRef,
    isOpenMenu,
    setIsOpenMenu,
  }
}

/**
 * Clones a react element and applies a new className based on the `isOpenMenu`
 */
const applyOpacity = ({
  selectedOption,
  isOpenMenu,
}: {
  selectedOption?: ReactElement<{ className: string }>
  isOpenMenu: boolean
}) => {
  if (selectedOption) {
    return cloneElement(selectedOption, {
      className: `${selectedOption.props.className} ${isOpenMenu ? 'option-view--back-option' : ''}`,
    })
  }

  return null
}

export default Dropdown
