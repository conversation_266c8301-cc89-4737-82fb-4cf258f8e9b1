import { useTranslate } from '../hooks/useTranslate'
import { TextErrorProps } from '../types/Text.types'

/** Renders a text with red color to serve as */
const TextError = ({
  className = '',
  dataTestID,
  validationObject,
  errorText,
  position = 'absolute',
}: TextErrorProps) => {
  const t = useTranslate()
  return (
    <p
      // Do not change the "error-text" class
      className={`error-text ${className}`}
      data-testid={dataTestID}
      style={{
        color: 'red',
        position,
      }}
    >
      {errorText
        ? errorText
        : //@ts-ignore
          t(validationObject?.i18nKey, validationObject?.values)}
    </p>
  )
}

export default TextError
