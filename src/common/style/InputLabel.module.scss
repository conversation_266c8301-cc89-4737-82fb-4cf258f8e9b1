@use './abstracts/mixins';
@use './abstracts/colors';
@use './abstracts/variables';

/** @define inputLabel */
.inputLabel {
  @include mixins.flex-layout($justify-content: flex-start,
    $align-items: center);
  gap: 5px;
  margin-top: -25px;

  &__input-label {
    @include mixins.input-label;
    @include mixins.no-user-select;
    @include mixins.text-ellipsis;
    text-transform: capitalize;
    display: inline-block;
    position: absolute;
    top: -1.75rem;

    &--alternate {
      color: colors.$blue;
      text-align: right;
      width: 100%;
      font-weight: variables.$font-bold;
    }
  }

  &__tooltip-label {
    position: relative;
    top: unset;
    width: unset;
  }

  &__icon {
    cursor: pointer;
    width: 15px;
    height: 15px;
  }
}