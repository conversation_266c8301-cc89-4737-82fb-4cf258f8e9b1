@use './abstracts/colors';
@use './abstracts/mixins';
@use './abstracts/variables';

$main-padding: 5px 0;
$slider-thumb-circle: url('../../assets/OnboardingSlider.svg');
$slider-thumb-yellow: url('../../assets/OnboardingSliderYellow.svg');

@mixin slider-thumb($height: 28px,
  $width: 34px,
  $thumb-bg-img: $slider-thumb-circle,
  $cursor: grab) {
  background: $thumb-bg-img;
  -webkit-appearance: none;
  appearance: none;
  background-size: cover;
  border: none;
  height: $height;
  overflow: visible;
  width: $width;
  cursor: $cursor;
}

//Used to specify the style when the slider thumb is being dragged
@mixin slider-thumb-active($thumb-bg-img: $slider-thumb-circle, $cursor: grab) {
  &::-ms-thumb {
    @include slider-thumb($thumb-bg-img: $thumb-bg-img, $cursor: $cursor);

    &:active {
      cursor: grabbing !important;
    }
  }

  &::-moz-range-thumb {
    @include slider-thumb($thumb-bg-img: $thumb-bg-img, $cursor: $cursor);

    &:active {
      cursor: grabbing !important;
    }
  }

  &::-webkit-slider-thumb {
    @include slider-thumb($thumb-bg-img: $thumb-bg-img, $cursor: $cursor);

    &:active {
      cursor: grabbing;
    }
  }
}

@mixin range($thumb-bg-img: $slider-thumb-circle, $cursor: grab) {
  border-radius: variables.$rounded;
  height: 5px;
  outline: none;
  -webkit-transition: 0.2s;
  transition: opacity 0.2s;
  width: 100%;

  @include mixins.appearance;
  @include slider-thumb-active($thumb-bg-img, $cursor: $cursor);

  &:focus {
    outline: none;
  }

  &::-moz-range-progress {
    height: 5px;
    border-radius: variables.$rounded;
  }
}

@mixin controls($bg-color: colors.$blue-faint) {
  background-color: $bg-color;
  padding: 15px;
  border-radius: variables.$rounded;
  cursor: pointer;
  @include mixins.no-user-select;

  &:active {
    //When the slider buttons are clicked change icon color and background color
    background-color: $bg-color !important;

    .sliderInput__icon {
      //Changes the svg color to white
      filter: brightness(0) invert(1) !important;
    }
  }

  &--disabled {
    cursor: not-allowed;
    @include mixins.disabled(0.4);
  }
}

@mixin icon($filter-color: invert(39%) sepia(48%) saturate(938%) hue-rotate(171deg) brightness(94%) contrast(90%)) {
  z-index: variables.$slider-input-icon-z-index;
  font-size: variables.$font-size-xlarge;
  color: colors.$blue-light;
  filter: $filter-color;
  height: 20px;
  width: 20px;
  @include mixins.flex-layout($align-items: center);
}

/** @define sliderInput */
.sliderInput {
  background-color: colors.$white;
  border-radius: variables.$rounded;
  padding-bottom: 2rem;

  @media only screen and (max-width: variables.$mobile-devices) {
    @include mixins.non-clickable-slider;
  }

  &__tip {
    width: 0;
    height: 0;
    border-left: 15px solid transparent;
    border-right: 15px solid transparent;
    border-top: 15px solid colors.$blue;
  }

  &__main-container {
    position: relative;
    flex: 50%;
    width: 100%;

    @media only screen and (max-width: variables.$mobile-devices) {
      width: 100%;
    }
  }

  &__range-container {
    position: relative;
  }

  &__bubble {
    position: absolute;
    bottom: 35px;
    @include mixins.no-user-select;
  }

  &__disabled-msg {
    position: absolute;
    z-index: 9999;
    box-shadow: variables.$chart-box-shadow;
    left: 50%;

    transform: translateX(-50%);
    width: 80%;
    @include mixins.font-style($line-height: 1.2 !important);
  }

  &--disabled {
    opacity: 0.4;
    pointer-events: none;
    cursor: not-allowed;
    background-color: colors.$white;
    padding-bottom: 2rem;
    border-radius: variables.$rounded;
    @include mixins.no-user-select;
  }

  &__inner {
    width: 100%;
    gap: 5px;
    @include mixins.font-style($font-size: variables.$font-size-large,
      $font-weight: variables.$font-semibold );
    @include mixins.flex-layout;
  }

  &__value {
    cursor: pointer;
    text-align: center;
    border: 0;
    outline: 0;
    @include mixins.no-user-select;

    &--disabled {
      cursor: not-allowed;
      pointer-events: none;
      @include mixins.no-user-select;
    }
  }

  &__icon {
    @include icon;

    &--yellow {
      @include icon($filter-color: colors.$yellow-image);
    }
  }

  &__controls {
    @include controls;

    &--yellow {
      @include controls($bg-color: colors.$yellow-faint);
    }
  }

  &__container {
    background-color: colors.$white;
    border-radius: variables.$rounded;
    border: 1px solid colors.$gray-mist;
    margin-bottom: 0.9375rem;
    position: relative;
    @include mixins.flex-layout(row, flex-start);
  }

  &__range {
    @include range;

    &--yellow {
      @include range($thumb-bg-img: $slider-thumb-yellow);
    }
  }
}