import i18n, { languages } from '../../config/i18n'
import { ACCOUNT_MENU } from '../../routes/Route'
import Card from '../components/card/Card'
import Layout from '../components/Layout'
import NavigationButtons from '../components/NavigationButtons'
import { useCustomNavigation } from '../hooks/useCustomNavigation'
import { useTranslate } from '../hooks/useTranslate'
import style from '../style/LanguageSettings.module.scss'

const LanguageSettings = () => {
  const t = useTranslate()
  const navigate = useCustomNavigation()

  return (
    <Layout
      containerWidth="small"
      pageTitle={t('LANGUAGE_SETTINGS.PAGE_TITLE')}
      navigateTo={ACCOUNT_MENU.SETTINGS}
      bottomSection={
        <NavigationButtons
          hideActionButton
          onClickFirst={() => navigate(ACCOUNT_MENU.SETTINGS)}
        />
      }
    >
      <section className={style['language-settings__container']}>
        {languages.map((language) => {
          return (
            <Card
              key={language.value}
              title={language.fullName}
              subTitle={language.value}
              variant="gray-dirty"
              interactEnabled
              headerImage={language.icon}
              onClick={() => i18n.changeLanguage(language.value)}
              alert={language.value === i18n.language ? 'completed' : undefined}
            />
          )
        })}
      </section>
    </Layout>
  )
}

export default LanguageSettings
