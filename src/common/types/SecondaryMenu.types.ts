import { ReactNode } from 'react'

type MenuCardConfig = {
  title: string
  variant?: 'alternative'
  comingSoon?: boolean
  items: Array<{
    mainText: string
    to?: string
    icon: string
    writeProtected?: boolean
    dataTestID?: string
    disabled?: boolean
    cardVariant?: 'gray-dirty'
  }>
}

type SecondaryMenuProps = {
  navigateTo: string
  pageTitle: string
  menuCards: Array<MenuCardConfig>
  children?: ReactNode
}

export type { MenuCardConfig, SecondaryMenuProps }
