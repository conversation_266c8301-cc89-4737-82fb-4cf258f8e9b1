import { ReactNode } from 'react'

type PageContentProps = {
  icon?: string
  title?: string
  locizeKey?: string
  mainContent?: string | ReactNode
  values?: object
  textArea?: ReactNode
  setTextAreaFeedback?: () => void
  className?: string
  smallIcon?: string
  value?: string
  backgroundColor?: 'white' | 'default'
}

type PageLayoutProps = {
  children: ReactNode
  headerTitle?: string
  headerTextColor?: 'default' | 'blue'
  containerWidth?: 'small' | 'medium'
  containerHeight?: 'sh' | 'lh' | 'xlh' | 'auto' | 'lite-build'
  containerMt?: 'nomt' | 'mt-20'
  headerVariant?: 'spaced'
  layoutVariant?: 'sun-bg' | 'sun-blue-bg'
}

export type { PageContentProps, PageLayoutProps }
