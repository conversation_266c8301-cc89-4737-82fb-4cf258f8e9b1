import { ReactNode } from 'react'
import { To } from 'react-router-dom'

type LayoutProps = {
  children?: ReactNode
  pageTitle?: string
  navigateTo?: To
  onClickAction?: () => void
  headerTitle?: string
  hideMobileHeader?: boolean
  className?: string
  card?: ReactNode
  bottomSection?: ReactNode | null
  topSection?: ReactNode
  hideBottomSectionDivider?: boolean
  hideDividerHeader?: boolean
  dividersClass?: string
  textNextToHeader?: string
  headerTextColor?: 'default' | 'blue'
  containerWidth?: 'small' | 'medium'
  headerVariant?: 'spaced'
  layoutVariant?: 'sun-bg' | 'sun-blue-bg'
  containerHeight?: 'sh' | 'lh' | 'xlh' | 'auto' | 'lite-build'
  containerMt?: 'nomt' | 'mt-20'
}

export type { LayoutProps }
