@use '../../../common/style/abstracts/mixins';
@use '../../../common/style/abstracts/variables';

/* @define filters */
.filters {
  width: 100%;
  padding: 1px;
  border-radius: variables.$rounded;

  &__container {
    padding: 1.5625rem 1.5625rem 0.625rem 1.5625rem;
    width: 100%;
    box-shadow: variables.$generic-shadow;
    @include mixins.flex-layout(
      $flex-direction: column,
      $align-items: flex-start
    );
    flex-shrink: 0;
  }

  &__date-dropdown {
    width: 100%;
  }

  &__date-range {
    width: 100%;
    @include mixins.flex-layout(
      $justify-content: space-between,
      $gap: 1.875rem
    );
    @media only screen and (max-width: variables.$mobile-devices) {
      flex-direction: column;
      gap: 0;
    }
  }
}
