import { assign, setup } from 'xstate'
import {
  changeProduct,
  storeBankingInfo,
  updateReturns,
  wipeBankFetchError,
} from './BankMachineActions'
import {
  isAuthenticated,
  isInvestmentAccountOpen,
  isLiteBuild,
} from './BankMachineGuards'
import { getReturns, readUserBankingInfo } from './BankMachineServices'
import {
  bankPromiseActorInput,
  bankPromiseToPromiseActor,
} from './BankMachineUtils'
import {
  BankMachineContext,
  BankMachineEvent,
} from './types/BankMachineTypes.type'

const initialContextValues: BankMachineContext = {
  bankingInfo: {
    nominalBalance: [],
    payoutHistory: [],
    payinHistory: [],
    nextPayout: null,
    all: [],
  },
  bankingInfoError: undefined,
  tontineProduct: 'TontineTrustFund',
  returns: undefined,
}

/**
 * Bank machine configuration
 */
export const bankMachine = setup({
  types: {
    context: {} as BankMachineContext,
    events: {} as BankMachineEvent,
  },
  actors: {
    readUserBankingInfo: bankPromiseToPromiseActor(readUserBankingInfo),
    getReturnsActor: bankPromiseToPromiseActor(getReturns),
  },
  actions: {
    updateReturns: assign(updateReturns),
    wipeBankFetchError: assign(wipeBankFetchError),
    changeProduct: assign(changeProduct),
    storeBankingInfo: assign(storeBankingInfo),
  },
  guards: {
    isLiteBuild,
    isAuthenticated,
    isInvestmentAccountOpen,
  },
}).createMachine({
  context: initialContextValues,
  id: 'BankMachine',
  initial: 'IDLE',
  states: {
    IDLE: {
      on: {
        UPDATE_PRODUCT: {
          actions: { type: 'changeProduct' },
        },
        FETCH_BANKING_INFO: {
          target: 'FETCHING_BANK_INFO',
        },
        GET_RETURNS: {
          target: 'GETTING_RETURNS',
        },
      },
    },
    GETTING_RETURNS: {
      invoke: {
        src: 'getReturnsActor',
        id: 'getReturnsActorID',
        input: bankPromiseActorInput,
        onDone: [
          {
            guard: 'isLiteBuild',
            target: 'IDLE',
            actions: [{ type: 'updateReturns' }],
          },
          {
            guard: 'isInvestmentAccountOpen',
            target: 'FETCHING_BANK_INFO',
            actions: [{ type: 'updateReturns' }],
          },
          {
            target: 'IDLE',
            actions: [{ type: 'updateReturns' }],
          },
        ],
        onError: {
          target: 'IDLE',
        },
      },
    },

    FETCHING_BANK_INFO: {
      invoke: {
        src: 'readUserBankingInfo',
        id: 'readUserBankingInfoID',
        input: bankPromiseActorInput,
        onError: {
          target: 'IDLE',
        },
        onDone: {
          target: 'IDLE',
          actions: [
            { type: 'storeBankingInfo' },
            { type: 'wipeBankFetchError' },
          ],
        },
      },
      description:
        'Makes a parallel request to multiple BS APIs to fetch all banking information',
    },
  },
})
