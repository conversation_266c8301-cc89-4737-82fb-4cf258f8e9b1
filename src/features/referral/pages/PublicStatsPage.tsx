import Layout from '../../../common/components/Layout'
import SuspenseLoader from '../../../common/components/SuspenseLoader'
import { useLocalization } from '../../../common/hooks/useLocalization'
import { useTranslate } from '../../../common/hooks/useTranslate'
import SignInLiteUI from '../../authentication/components/SignInLiteUI'
import VerifyEmailError from '../../authentication/components/VerifyEmailError'
import { useLiteAuth } from '../../authentication/hooks/useLiteAuth'
import ReferralStats from '../components/ReferralStats'

/**
 * Renders a public referral stats page for the MyTontine lite
 * referral program
 */
const PublicStatsPage = () => {
  const t = useTranslate()
  const { detectedCountry } = useLocalization()

  const { isLoading, referralDetails, isAuth, error, verifyToken } =
    useLiteAuth()

  if (verifyToken && isLoading && !referralDetails) {
    return <SuspenseLoader />
  }

  return (
    <Layout
      containerWidth="medium"
      containerHeight="lite-build"
      containerMt="nomt"
      hideMobileHeader
      hideDividerHeader
      headerTitle={t('P_INVITE_FRIENDS_TITLE', {
        currencySymbol: detectedCountry.currencySymbol,
      })}
      headerVariant="spaced"
      headerTextColor="blue"
    >
      {!isAuth && <SignInLiteUI />}

      {error && <VerifyEmailError errorText={t('EMAIL_VERIFICATION_ERROR')} />}

      {!error && isAuth ? (
        <ReferralStats
          redeemCount={referralDetails?.redeemCount ?? '-'}
          referralCode={referralDetails?.referralCode}
        />
      ) : (
        !error && <ReferralStats redeemCount={'-'} referralCode={''} />
      )}
    </Layout>
  )
}

export default PublicStatsPage
