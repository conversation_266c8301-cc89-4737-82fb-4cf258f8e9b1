import { lazy, useState } from 'react'
import Card from '../../../common/components/card/Card'
import Layout from '../../../common/components/Layout'
import { ASSET } from '../../../common/constants/Assets'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { PUBLIC } from '../../../routes/Route'
import { useAccountService } from '../../authentication/hooks/useAccountService'
import ReferralLinkView from '../components/ReferralLinkView'
import style from '../style/ReferralDetails.module.scss'
import ViewRewards from './ViewRewards'

const ReferralLinkModal = lazy(() => import('../components/ReferralLinkModal'))

/**
 * Renders user referral details, also allows the user to edit their referral
 * code
 */
const ReferralDetails = () => {
  //Hooks
  const t = useTranslate()

  const {
    referralCode,
    editingLimitReached,
    editReferralCodeModal,
    setEditReferralCodeModal,
    renderViewRewards,
    setRenderViewRewards,
  } = useReferralDetailsController()

  if (renderViewRewards) {
    return <ViewRewards onClickBack={() => setRenderViewRewards(false)} />
  }

  return (
    <Layout
      className={style.referral}
      containerHeight="lh"
      pageTitle={t('REWARDS.INVITE_FRIENDS_PAGE_TITLE')}
      navigateTo={PUBLIC.GO_TO_PREV_PAGE}
      containerWidth="small"
      containerMt="nomt"
    >
      <ReferralLinkView
        editingLimitReached={editingLimitReached}
        openEditReferralCodeModal={setEditReferralCodeModal}
        referralLink={referralCode}
        label={t('INVITE_FRIENDS.UNIQUE_LINK_LABEL')}
        hideDescription={editingLimitReached}
      />

      <div onClick={() => setRenderViewRewards(true)}>
        <Card
          headerImage={ASSET.iconaccountrewardbicolor}
          title={t('VIEW.EARNED.REWARDS')}
          variant="gray-dirty"
          headerImageSize="large"
          interactEnabled
          showArrow
        />
      </div>

      {editReferralCodeModal && (
        <ReferralLinkModal
          referralLink={referralCode}
          setOpenEditModal={setEditReferralCodeModal}
          isOpen={editReferralCodeModal}
        />
      )}
    </Layout>
  )
}

const useReferralDetailsController = () => {
  const { context, states } = useAccountService()
  //State
  const [editReferralCodeModal, setEditReferralCodeModal] = useState(false)
  const [renderViewRewards, setRenderViewRewards] = useState(false)

  return {
    editingLimitReached:
      context?.user_details?.referralDetails?.editingLimitReached ?? false,
    referralCode: context?.user_details?.referralDetails?.referral_code ?? '',
    context,
    states,
    editReferralCodeModal,
    setEditReferralCodeModal,
    renderViewRewards,
    setRenderViewRewards,
  }
}

export default ReferralDetails
