@use '../../../common/style/abstracts/colors';
@use '../../../common/style/abstracts/mixins';
@use '../../../common/style/abstracts/variables';

/* @define referral **/
.referral {
  &__card {
    max-width: 100% !important;
  }

  &__card-promo {
    max-width: 100% !important;
    height: 6.875rem;
  }

  &__link-view-container {
    @include mixins.flex-layout(column);
    margin: 2.5rem 0;
  }

  &__link-container {
    width: 100%;
  }

  &__link-center {
    cursor: pointer;
    &--copy {
      cursor: pointer;
    }
  }

  &__edit-link-section {
    margin-bottom: 1.25rem;
  }

  &__edit-disclaimer {
    @include mixins.font-style($font-size: variables.$font-size-s);
  }

  &__link-view {
    background-color: colors.$gray-cloud !important;
    margin-bottom: 10px;
  }

  &__link-view-desc {
    @include mixins.font-style($font-size: variables.$font-size-s);
    @include mixins.no-user-select;
    text-align: left;
  }
}
