import Card from '../../../common/components/card/Card'
import { ASSET } from '../../../common/constants/Assets'
import { useLocalization } from '../../../common/hooks/useLocalization'
import { useTranslate } from '../../../common/hooks/useTranslate'
import style from '../style/StatCards.module.scss'
import { StatCardsProps } from '../types/ReferralTypes.type'

const rewardAmount = 250

/**
 * Renders referral stats with currency values depending where the user is from
 */
const StatCards = ({
  redeemCount = 0,
  countTitle,
  valueTitle,
}: StatCardsProps) => {
  const { formatAmount, detectedCountry } = useLocalization()
  const t = useTranslate()

  return (
    <section className={style['stat-cards']}>
      <article className={style['stat-cards__card-con']}>
        <Card
          headerImage={ASSET.referredWhite}
          headerImageSize="x-large"
          title={countTitle}
          subTitle={redeemCount?.toString()}
          variant="stat"
        />
        <p className={style['stat-cards__card-explainer-text']}>
          {t('TOTAL_REF_EXPLAINER')}
        </p>
      </article>

      <article className={style['stat-cards__card-con']}>
        <Card
          headerImage={ASSET.incentiveWhite}
          headerImageSize="x-large"
          title={valueTitle}
          subTitle={
            typeof redeemCount === 'number'
              ? formatAmount({
                  amount: redeemCount * rewardAmount,
                  //FIXME: To make everything consistent only USD and EUR supported
                  currency: detectedCountry?.currency === 'USD' ? 'USD' : 'EUR',
                  style: 'currency',
                  digits: { maximumFractionDigits: 0 },
                })?.formattedAmountWithSymbol
              : `-`
          }
          variant="stat"
        />
        <p className={style['stat-cards__card-explainer-text']}>
          {t('PENDING_REWARDS_EXPLAINER', {
            currencySymbol: detectedCountry.currencySymbol,
          })}
        </p>
      </article>
    </section>
  )
}

export default StatCards
