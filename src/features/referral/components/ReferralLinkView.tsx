import SocialShare from '../../../common/components/SocialShare'
import TextInput from '../../../common/components/TextInput'
import { ASSET } from '../../../common/constants/Assets'
import { UI_TEST_ID } from '../../../common/constants/DataTestIDs'
import { useLocalization } from '../../../common/hooks/useLocalization'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { copyToClipboard, debounce } from '../../../common/utils/UtilFunctions'
import { isLite } from '../../../config/lite'
import { useAccountService } from '../../authentication/hooks/useAccountService'
import style from '../style/ReferralDetails.module.scss'
import { ReferralLinkViewProps } from '../types/ReferralTypes.type'
import { REFERRAL_CONSTANTS } from '../utils/consts'

/**
 * Debounced copy to clipboard in order not to spam toast messages
 */
const debouncedCopy = debounce(
  copyToClipboard,
  REFERRAL_CONSTANTS.SEARCH_DEBOUNCE_MS
)

/**
 * Displays user's referral link and opens a referral link edit modal
 */
const ReferralLinkView = ({
  editingLimitReached,
  openEditReferralCodeModal,
  referralLink,
  label,
  className,
  hideDescription,
}: ReferralLinkViewProps) => {
  const t = useTranslate()
  const { context } = useAccountService()
  const { detectedCountry } = useLocalization()

  const liteFull = !isLite
    ? `${context?.liteData?.websiteOrigin ?? `${window?.origin}/`}`
    : `${context?.liteData?.websiteOrigin}/`

  const referralLinkFull = `${liteFull}${referralLink}`

  /**
   * Makes the edited referral code not clickable if the edit limit
   * has been reached
   */
  const editReferralCode = () => {
    if (editingLimitReached) {
      debouncedCopy(referralLinkFull, t('REFERRAL_LINK_TO_CLIPBOARD'))
    } else {
      openEditReferralCodeModal?.(true)
    }
  }

  return (
    <section
      className={`${className ?? ''} ${style[`referral__link-view-container`]}`}
    >
      <section className={style[`referral__link-container`]}>
        <div
          onClick={
            editingLimitReached && !isLite ? undefined : editReferralCode
          }
          className={
            style[`referral__link-center${editingLimitReached ? '--copy' : ''}`]
          }
        >
          <TextInput
            label={label}
            value={referralLinkFull}
            suffixIcon={
              editingLimitReached
                ? ASSET.iconaccountcopy
                : ASSET.icononboardinarrowforward
            }
            readOnly
            className={style[`referral__link-view`]}
            dataTestID={UI_TEST_ID.editRefLinkButton}
          />
        </div>
        {!hideDescription && (
          <p className={style['referral__link-view-desc']}>
            {t('INVITE_FRIENDS.UNIQUE_LINK_DESC')}
          </p>
        )}
        <br />
        <SocialShare
          size={40}
          urlToShare={referralLinkFull}
          postTitle={t('SHARE_LINK_TITLE')}
          postContent={t('SHARE_LINK_CONTENTS', {
            referralLink: referralLinkFull,
            currencySymbol: detectedCountry.currencySymbol,
          })}
          facebookHashtag={t('FACEBOOK_HASHTAG')}
          hashTags={[...t('TWITTER_HASHTAGS').split(',')]}
          twitterAccountsToFollow={[
            ...t('TWITTER_ACCOUNTS_TO_FOLLOW').split(','),
          ]}
          roundIcons
        />
      </section>
    </section>
  )
}

export default ReferralLinkView
