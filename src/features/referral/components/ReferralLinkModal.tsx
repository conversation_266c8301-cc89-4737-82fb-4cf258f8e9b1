import Button from '../../../common/components/Button'
import ConfirmationModal from '../../../common/components/confirmation-modal/ConfirmationModal'
import TextInput from '../../../common/components/TextInput'
import { UI_TEST_ID } from '../../../common/constants/DataTestIDs'
import { INPUT_LIMIT } from '../../../common/constants/InputLimits'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { ValidationData } from '../../../common/types/CommonTypes.types'
import { COMMON_CONSTANTS } from '../../../common/utils/consts'
import { isLite } from '../../../config/lite'
import { useAccountService } from '../../authentication/hooks/useAccountService'
import { useEditReferralCode } from '../hooks/useEditReferralCode'
import style from '../style/ReferralDetails.module.scss'
import { ReferralLinkModalProps } from '../types/ReferralTypes.type'
import { REFERRAL_CONSTANTS } from '../utils/consts'

const block = 'referral'

/**
 * This component is used to display and change the user's referral
 * link which he or she will then share with their friends in the future.
 */
const ReferralLinkModal = ({
  referralLink,
  setOpenEditModal,
  isOpen,
}: ReferralLinkModalProps) => {
  const t = useTranslate()
  const { context } = useAccountService()

  const {
    customReferralCode,
    handleOnChange,
    validateReferralCode,
    referralCodeValidated,
    handleSaveReferralCode,
    onModalClose,
    shouldDisableSaveButton,
    isLoading,
  } = useEditReferralCode({ referralLink, setOpenEditModal })

  const liteFull = !isLite
    ? `${context?.liteData?.websiteOrigin ?? `${window?.origin}/`}`
    : `${context?.liteData?.websiteOrigin}/`

  return (
    <>
      {isOpen && (
        <ConfirmationModal
          isOpen={isOpen}
          title={t('INVITE_FRIENDS.LINK_MODAL_TITLE')}
          content={`${t('INVITE_FRIENDS.LINK_MODAL_CUSTOM_LABEL')} 
            ${liteFull}${REFERRAL_CONSTANTS.REFERRAL_CODE_PREFIX}${customReferralCode}`}
        >
          <section className={style[`${block}__edit-link-section`]}>
            <TextInput
              value={customReferralCode}
              onChange={handleOnChange}
              errorMessage={referralCodeValidated as ValidationData}
              validatorFunction={validateReferralCode}
              onKeyDown={({ keyCode }) => {
                if (keyCode === COMMON_CONSTANTS.ENTER_KEY) {
                  handleSaveReferralCode()
                }
              }}
              maxLength={INPUT_LIMIT.REFERRAL_CODE_MAX}
              dataTestID={UI_TEST_ID.editReferralLinkInput}
            />

            <p className={style[`${block}__edit-disclaimer`]}>
              {t('INVITE_FRIENDS.LINK_CHANGE_ALERT')}
            </p>
          </section>
          <Button
            disabled={shouldDisableSaveButton()}
            onClick={handleSaveReferralCode}
            loading={isLoading}
            dataTestID={UI_TEST_ID.saveReferralCodeButton}
          >
            {t('INVITE_FRIENDS.MODAL_BTN_SAVE_TEXT')}
          </Button>

          <Button onClick={onModalClose} variant="alternative">
            {t('CONFIRMATION_MODAL_BUTTONS.CANCEL')}
          </Button>
        </ConfirmationModal>
      )}
    </>
  )
}

export default ReferralLinkModal
