import SocialShare from '../../../common/components/SocialShare'
import { useLocalization } from '../../../common/hooks/useLocalization'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { isLite } from '../../../config/lite'
import { useAccountService } from '../../authentication/hooks/useAccountService'
import style from '../style/ReferralStats.module.scss'
import { ReferralStatsProps } from '../types/ReferralTypes.type'
import ReferralCode from './ReferralCode'
import StatCards from './StatCards'

/**
 * Renders referral stats with referral code CTAs
 */
const ReferralStats = ({
  referralCode,
  redeemCount,
  hideCount,
}: ReferralStatsProps) => {
  const t = useTranslate()
  const { context } = useAccountService()
  const { detectedCountry } = useLocalization()

  const liteFull = !isLite
    ? `${context?.liteData?.websiteOrigin ?? `${window?.origin}/`}`
    : `${context?.liteData?.websiteOrigin}/`

  const referralLinkFull = `${liteFull}${referralCode}`

  return (
    <>
      {referralCode && (
        <>
          <ReferralCode
            referralCode={referralLinkFull}
            label={t('REF_CODE_DISPLAY_BOX')}
          />

          <div className={style['referral-stats__social-share']}>
            <SocialShare
              size={40}
              urlToShare={referralLinkFull}
              postTitle={t('SHARE_LINK_TITLE', {
                currencySymbol: detectedCountry.currencySymbol,
              })}
              postContent={t('SHARE_LINK_CONTENTS', {
                currencySymbol: detectedCountry.currencySymbol,
                referralLink: referralLinkFull,
              })}
              facebookHashtag={t('FACEBOOK_HASHTAG')}
              hashTags={[...t('TWITTER_HASHTAGS').split(',')]}
              twitterAccountsToFollow={[
                ...t('TWITTER_ACCOUNTS_TO_FOLLOW').split(','),
              ]}
              roundIcons
            />
          </div>
        </>
      )}

      {!hideCount && (
        <>
          <h3 className={style['referral-stats']}>{t('STATS_GROUP_TITLE')}</h3>
          <StatCards
            countTitle={t('STATS_REDEEM_COUNT')}
            valueTitle={t('STATS_REDEEM_VALUE')}
            redeemCount={redeemCount ?? '-'}
          />
        </>
      )}
    </>
  )
}

export default ReferralStats
