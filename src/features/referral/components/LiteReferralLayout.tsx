import Button from '../../../common/components/Button'
import TextError from '../../../common/components/TextError'
import TitleAndClickableText from '../../../common/components/TitleAndClickableText'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { postMessageToParent } from '../../../common/hooks/useWindowPostMessage'
import { useLiteAuth } from '../../authentication/hooks/useLiteAuth'
import style from '../style/LiteReferralLayout.module.scss'
import ReferralStats from './ReferralStats'

/**
 * Renders the referral code, social share and a cta button that takes the user
 * to the referral page.
 *
 * @note The cta button only works if the APP is in an iframe or has parent
 * window, otherwise it won't work since it's not in a parent window
 */
const LiteReferralLayout = () => {
  const t = useTranslate()
  const { referralDetails, error } = useLiteAuth()

  if (error) {
    return (
      <section className={style['lite-referral-layout']}>
        <div style={{ textAlign: 'center' }}>
          <TextError errorText={error?.translatedError} />
        </div>
      </section>
    )
  }

  if (referralDetails) {
    return (
      <section className={style['lite-referral-layout']}>
        <TitleAndClickableText title={t('AUTH_TITLE_BOTTOM')} />
        <ReferralStats referralCode={referralDetails?.referralCode} hideCount />
        <Button
          onClick={() =>
            postMessageToParent({
              eventId: 'REDIRECT',
              eventData: {
                path: '/rewards-club/#referral',
              },
            })
          }
          trackActivity={{
            trackId: 'referral_lite_navigate_to_referral',
          }}
        >
          {t('CLICKABLE_TEXT_BOTTOM')}
        </Button>
      </section>
    )
  }

  return null
}

export default LiteReferralLayout
