import { useState } from 'react'
import Card from '../../../common/components/card/Card'
import NavigationCard from '../../../common/components/NavigationCard'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { idVerificationAlertStatus } from '../../../common/utils/UtilFunctions'
import { PERSONAL_DETAILS } from '../../../routes/Route'
import { isDisabled } from '../../DisabledLaunchFeatures'
import { useAccountService } from '../hooks/useAccountService'
import FaceScan from '../pages/FaceScan'
import { idVerificationStatusText } from '../utils/UtilsFunctions'

/**
 * Renders a list of CTA cards for L1 KYC, and shows the user their L1 KYC
 * Status
 */
const L1KycList = () => {
  const t = useTranslate()
  const { context } = useAccountService()
  const {
    kyc_status: {
      L1: {
        requirements: { face_scanned, id_verified, phone_verified },
      },
    },
  } = context.user_details || { kyc_status: { L1: { requirements: {} } } }

  const [faceScan, setFaceScan] = useState<boolean>(false)

  const showFaceScan = () => setFaceScan(true)

  return (
    <>
      {faceScan && (
        <FaceScan
          scanType="match-id"
          asModal
          onClickExitScan={() => setFaceScan(false)}
        />
      )}
      <Card
        title={t('KYC.FACE_ENROLLED')}
        onClick={face_scanned ? undefined : showFaceScan}
        disabled={isDisabled}
        alert={face_scanned ? 'completed' : 'warn'}
        variant="gray-dirty"
        showArrow
        arrowInvisible={face_scanned}
        interactEnabled={!face_scanned}
      />
      <Card
        title={idVerificationStatusText(context.user_details?.id_review_status)}
        onClick={!id_verified ? showFaceScan : undefined}
        disabled={isDisabled}
        variant="gray-dirty"
        showArrow
        arrowInvisible={id_verified}
        alert={idVerificationAlertStatus(
          context.user_details?.id_review_status
        )}
        interactEnabled={!id_verified}
      />
      <NavigationCard
        title={t('PHONE_NUMBER_INPUT_LABEL')}
        alert={phone_verified ? 'completed' : 'warn'}
        variant="gray-dirty"
        showArrow
        navigateTo={PERSONAL_DETAILS.PHONE_NUMBER}
        interactEnabled
      />
    </>
  )
}

export default L1KycList
