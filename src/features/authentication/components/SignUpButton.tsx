import { Link } from 'react-router-dom'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { PUBLIC } from '../../../routes/Route'
import style from '../style/LoginForm.module.scss'

/**
 * Renders a sign-up button with a link to the sign-up page.
 */
const SignUpButton = () => {
  const t = useTranslate()
  return (
    <section className={style['login-form__sign-up-section']}>
      <span>{t('LOGIN_FORM.SIGN_UP_FOR_ACCOUNT')}</span>
      <Link to={PUBLIC.SIGN_UP}>
        <span className={style['login-form__sign-up-btn']}>
          {t('BUTTON_LABEL.SIGN_UP')}
        </span>
      </Link>
    </section>
  )
}

export default SignUpButton
