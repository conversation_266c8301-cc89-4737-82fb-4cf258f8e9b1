import Card from '../../../common/components/card/Card'
import { ASSET } from '../../../common/constants/Assets'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { isDisabled } from '../../DisabledLaunchFeatures'
import style from '../style/PersonalDetails.module.scss'
import { IDVerificationCardProps } from '../types/IDVerificationCard.types'
import { idVerificationStatusText } from '../utils/UtilsFunctions'

const IDVerificationCard = ({ status, onClick }: IDVerificationCardProps) => {
  const t = useTranslate()

  const idVerificationAlertStatus = () => {
    if (status === 'not_reviewed') {
      return ASSET.pending
    }
    if (status === 'rejected') {
      return ASSET.iconaccounterrorcirclelare
    }
    if (status === 'approved') {
      return ASSET.iconaccountcheckedreencirclemall
    }
    return ASSET.infoamber
  }

  return (
    <div className={style['personalDetails__id-verify']}>
      <Card
        title={t('ID_VERIFY_TITLE')}
        subTitle={idVerificationStatusText(status)}
        headerImage={ASSET.iconaccountiderify}
        headerImageSize="large"
        variant="gray-dirty"
        interactEnabled={!!onClick}
        onClick={onClick}
        disabled={isDisabled}
        secondaryIcon={idVerificationAlertStatus()}
      />
    </div>
  )
}

export default IDVerificationCard
