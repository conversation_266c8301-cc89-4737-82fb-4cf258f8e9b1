import TextInput from '../../../common/components/TextInput'
import { UI_TEST_ID } from '../../../common/constants/DataTestIDs'
import { TextInputProps } from '../../../common/types/Text.types'
import { showExamplePhoneNumber } from '../../../common/utils/UtilFunctions'
import { usePhoneInput } from '../hooks/usePhoneInput'
import style from '../style/PhoneInput.module.scss'
import DialCodeDropdown from './DialCodeDropdown'

type PhoneInputProps = Pick<
  TextInputProps<string>,
  | 'value'
  | 'onChange'
  | 'label'
  | 'validatorFunction'
  | 'errorMessage'
  | 'optional'
  | 'readOnly'
>

/**
 * Phone number input with dropdown for dial codes. The text input formats the
 * phone number based on the dial code's country international format
 */
const PhoneInput = ({
  value,
  onChange,
  label,
  validatorFunction,
  errorMessage,
  readOnly,
}: PhoneInputProps) => {
  const { dialCode, onChangeDialCode, interOnChange, formattedPhoneNumber } =
    usePhoneInput({
      onChange,
      value,
    })

  return (
    <section className={style['phone-input']}>
      <article className={style[`phone-input__inputs-con`]}>
        <DialCodeDropdown
          value={dialCode}
          onChange={onChangeDialCode}
          className={style[`phone-input__dial-code`]}
          readOnly={readOnly}
          label={label}
        />
        <TextInput
          value={formattedPhoneNumber}
          onChange={interOnChange}
          inputMode="numeric"
          pattern="[0-9]*"
          type="tel"
          placeholder={showExamplePhoneNumber(dialCode)}
          //verify if this works as it should
          maxLength={showExamplePhoneNumber(dialCode)?.length}
          validatorFunction={(phoneNumWithoutDialCode) => {
            if (phoneNumWithoutDialCode) {
              validatorFunction?.(`${dialCode}${phoneNumWithoutDialCode}`)
            } else {
              // Needed to render  a message saying "Empty phone num"
              validatorFunction?.(phoneNumWithoutDialCode)
            }
          }}
          errorMessage={errorMessage}
          className={style[`phone-input__phone-number`]}
          dataTestID={UI_TEST_ID.phoneInput}
          readOnly={readOnly}
        />
      </article>
    </section>
  )
}

export default PhoneInput
