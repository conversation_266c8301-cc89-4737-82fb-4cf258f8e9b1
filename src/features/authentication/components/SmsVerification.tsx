import { Trans } from 'react-i18next'
import TextError from '../../../common/components/TextError'
import { SmsVerificationProps } from '../../../common/types/CommonTypes.types'
import { parsePhoneNum } from '../../../common/utils/UtilFunctions'
import { useOtpController } from '../hooks/useOtpController'
import { useSubmitPin } from '../hooks/useSubmitPin'
import style from '../style/SmsVerification.module.scss'
import { PinType } from '../types/Pin.types'
import { AUTH_CONSTANTS } from '../utils/consts'
import PinInput from './PinInput'

/**
 * Renders 6 pin fields to verify the phone number via SMS. The
 * `VERIFY_PHONE_NUMBER` event is already included in the `<PinInput />` component
 */
const SmsVerification = ({
  onPhoneNumberVerified,
  onFailedPhoneVerification,
  unverifiedPhoneNumber,
}: SmsVerificationProps) => {
  const {
    onSuccessfulVerification,
    onFailedVerification,
    errorMessage,
    setErrorMessage,
  } = useOtpController({
    onPhoneNumberVerified,
    onFailedPhoneVerification,
  })

  const { setPin, pin, handleSubmitPin } = useSubmitPin({
    length: AUTH_CONSTANTS.OTP_FIELDS,
    authMachineEvent: 'VERIFY_PHONE_NUMBER',
    successCallback: onSuccessfulVerification,
    failureCallback: onFailedVerification,
  })

  const onChange = (pin: PinType) => {
    setErrorMessage('')
    setPin(pin)
    handleSubmitPin({
      pin: pin?.join(''),
      payload: {
        verification_code: pin?.join(''),
        phone_number: unverifiedPhoneNumber,
      },
    })
  }

  return (
    <>
      <section className={style['sms-verification']}>
        <p className={style[`sms-verification__message`]}>
          <Trans
            i18nKey={'SENT_SMS_MESSAGE'}
            values={{
              unverifiedPhoneNumber: parsePhoneNum(unverifiedPhoneNumber ?? '')
                ?.formattedFullPhoneNumber,
            }}
          />
        </p>
      </section>
      <PinInput
        pinLength={AUTH_CONSTANTS.OTP_FIELDS}
        autoComplete={'on'}
        errorMessage={errorMessage}
        onChange={onChange}
        values={pin}
      >
        {errorMessage && (
          <TextError position="relative" errorText={errorMessage} />
        )}
      </PinInput>
    </>
  )
}

export default SmsVerification
