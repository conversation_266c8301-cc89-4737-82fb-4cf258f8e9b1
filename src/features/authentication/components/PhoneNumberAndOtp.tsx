import { usePhoneInputController } from '../hooks/usePhoneInputController'
import { PhoneNumberAndOtpProps } from '../types/PhoneNumberVerification.type'
import PhoneInput from './PhoneInput'
import SmsVerification from './SmsVerification'

/**
 * Renders a phone number input, if the phone number is submitted for
 * verification then the SMS OTP fields are rendered instead
 */
const PhoneNumberAndOtp = ({
  onFailedPhoneVerification,
  onPhoneNumberVerified,
  onChange,
  unverifiedPhoneNumReady,
  phoneNumberValidated,
  validatePhoneNumber,
}: PhoneNumberAndOtpProps) => {
  const {
    unverifiedPhoneNumFromSs,
    unverifiedPhone,
    setUnverifiedPhone,
    removePersistedPhoneNumber,
  } = usePhoneInputController()

  if (unverifiedPhoneNumReady || unverifiedPhoneNumFromSs) {
    return (
      <SmsVerification
        onPhoneNumberVerified={(verifiedPhoneNumber) => {
          onPhoneNumberVerified(verifiedPhoneNumber)
          removePersistedPhoneNumber()
        }}
        onFailedPhoneVerification={() => onFailedPhoneVerification?.()}
        unverifiedPhoneNumber={
          unverifiedPhoneNumReady ?? (unverifiedPhoneNumFromSs as string)
        }
      />
    )
  }

  return (
    <PhoneInput
      validatorFunction={validatePhoneNumber}
      errorMessage={phoneNumberValidated}
      onChange={(unverifiedPhone) => {
        setUnverifiedPhone(unverifiedPhone)
        onChange?.(unverifiedPhone)
      }}
      value={unverifiedPhone}
    />
  )
}

export default PhoneNumberAndOtp
