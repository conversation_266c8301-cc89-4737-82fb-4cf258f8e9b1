import { lazy } from 'react'
import ErrorTextCountdown from '../../../common/components/ErrorTextCountdown'
import Layout from '../../../common/components/Layout'
import NavigationButtons from '../../../common/components/NavigationButtons'
import { useCustomNavigation } from '../../../common/hooks/useCustomNavigation'
import { ACCOUNT_MENU } from '../../../routes/Route'
import { usePhoneNumValidation } from '../hooks/usePhoneNumberValidation'
import { useUpdatePhoneController } from '../hooks/useUpdatePhoneController'

const PhoneNumberAndOtp = lazy(() => import('../components/PhoneNumberAndOtp'))

/**
 * Renders the phone number verification flow
 */
const UpdatePhoneNumber = () => {
  const navigate = useCustomNavigation()
  const {
    t,
    addPhoneNumber,
    setUnverifiedPhoneNumber,
    readyForVerification,
    onSuccessfulVerification,
    isLoading,
    setReadyForVerification,
    apiError,
    setApiError,
    removePersistedPhone,
  } = useUpdatePhoneController()

  const { phoneNumberValidated, validatePhoneNumber } = usePhoneNumValidation()

  return (
    <Layout
      pageTitle={t('PHONE_NUMBER_INPUT_LABEL')}
      navigateTo={ACCOUNT_MENU.PERSONAL_DETAILS}
      containerWidth="small"
      containerHeight="sh"
      bottomSection={
        <NavigationButtons
          onClickSecond={addPhoneNumber}
          secondButtonLoading={isLoading}
          hideActionButton={Boolean(readyForVerification)}
          disabledSecond={Boolean(apiError || !phoneNumberValidated?.valid)}
          onClickFirst={() => {
            // phone ready for verification, just remove it with back button
            // so the phone input renders again
            if (readyForVerification) {
              setReadyForVerification(undefined)
              removePersistedPhone()
            } else {
              // phone input is rendered on screen, so when the user presses
              // the back button they will go back to personal details
              navigate(ACCOUNT_MENU.PERSONAL_DETAILS)
            }
          }}
          secondButtonLabel={t('COMMON.CONTINUE_BUTTON')}
        />
      }
    >
      <PhoneNumberAndOtp
        onPhoneNumberVerified={onSuccessfulVerification}
        unverifiedPhoneNumReady={readyForVerification}
        onChange={setUnverifiedPhoneNumber}
        phoneNumberValidated={phoneNumberValidated}
        validatePhoneNumber={validatePhoneNumber}
      />
      {apiError && (
        <ErrorTextCountdown
          i18nKey={apiError?.translatedError}
          onCountDownFinished={() => setApiError(undefined)}
          secondsToCountFrom={apiError?.data?.expires_in_seconds}
        />
      )}
    </Layout>
  )
}

export default UpdatePhoneNumber
