import { useLayoutEffect, useState } from 'react'
import { NavigateFunction, useLocation } from 'react-router-dom'
import CountDownTimer from '../../../common/components/CountDownTimer'
import Icon from '../../../common/components/Icon'
import Modal from '../../../common/components/Modal'
import { ASSET } from '../../../common/constants/Assets'
import { useCustomNavigation } from '../../../common/hooks/useCustomNavigation'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { IncomeForecastParams } from '../../../common/types/CommonTypes.types'
import { MAGIC_LOGIN_PARAM } from '../../../common/utils/consts'
import { parseMagicLink } from '../../../common/utils/UtilFunctions'
import { DASHBOARD_NAVIGATION, PUBLIC } from '../../../routes/Route'
import { useLegalMachine } from '../../agreements/hooks/useLegalMachine'
import { allAgreements } from '../../agreements/utils/consts'
import { useBankingService } from '../../banking/hooks/useBankingService'
import { useAccountService } from '../hooks/useAccountService'
import style from '../style/MagicLogin.module.scss'
import { MagicLoginResponse } from '../types/AuthMachineTypes.type'

/**
 * @note **Do not wrap this component as a child under `<InitializeUI />`!**
 *
 * Exchanges a magic link token for an auth token. If the magic token is valid,
 * the UAS responds with an auth token that is stored in the AuthMachine's
 * context, so the auth token can be used globally to make authenticated API
 * requests
 */
const MagicLogin = () => {
  //HOOKS
  const location = useLocation()
  const navigate = useCustomNavigation()
  const t = useTranslate()
  const [apiError, setApiError] = useState<undefined | string>(undefined)
  const { send, isAuthenticated } = useAccountService()
  const { sendBankEvent } = useBankingService()
  const { sendLegalEvent } = useLegalMachine()

  useLayoutEffect(() => {
    // Close error modal if user navigates from the page
    return () => setApiError(undefined)
  }, [location?.pathname])

  useLayoutEffect(() => {
    if (
      !isAuthenticated &&
      parseMagicLink(location?.pathname, MAGIC_LOGIN_PARAM)?.param ===
        MAGIC_LOGIN_PARAM
    ) {
      send({
        type: 'REDEEM_MAGIC_TOKEN',
        payload: {
          magic_login_token: parseMagicLink(
            location?.pathname,
            MAGIC_LOGIN_PARAM
          )?.token,
          successCallback: (data) => {
            const response = data as MagicLoginResponse

            sendLegalEvent({
              type: 'FETCH_AGREEMENT',
              payload: {
                agreementTypes: allAgreements,
              },
            })

            sendBankEvent({
              type: 'GET_RETURNS',
              payload: {
                invAccOpen:
                  response?.userAccountInfo?.investment_account_status ===
                  'opened',
              },
            })

            navigateUser(
              navigate,
              DASHBOARD_NAVIGATION,
              response?.forecastParams
            )
          },
          failureCallback: (error) => {
            setApiError(error?.translatedError)
          },
        },
      })
    }
  }, [
    location?.pathname,
    send,
    navigate,
    isAuthenticated,
    sendLegalEvent,
    sendBankEvent,
  ])

  if (apiError) {
    return (
      <Modal isOpen backdrop>
        <main className={style['magic-login']}>
          <Icon
            fileName={ASSET.infoamber}
            className={style['magic-login__icon']}
          />
          <p className={style['magic-login__message']}>
            {t('ERROR_MAGIC_LINK_TEXT')}
          </p>
          <p className={style['magic-login__countdown']}>
            <CountDownTimer
              seconds={10}
              onCountdownFinished={() => {
                navigate(PUBLIC.SIGN_IN, { replace: true })
              }}
            />
          </p>
        </main>
      </Modal>
    )
  }

  return null
}

/**
 * Navigates the user to the tontinator page if there are forecast params
 * available, otherwise the user is redirected to the dashboard home page
 */
const navigateUser = (
  navigate: NavigateFunction,
  DASHBOARD_NAVIGATION: {
    TONTINATOR: '/mytt-dashboard/sandbox-tontinator'
    FUNDED_PROGRESS: '/mytt-dashboard/funded-progress'
  },
  forecastParams?: IncomeForecastParams
) => {
  //If user has forecast params navigate them to tontinator to do a forecast
  if (forecastParams) {
    navigate(DASHBOARD_NAVIGATION.TONTINATOR, {
      state: forecastParams,
      //Don't allow the user to go back to the magic_login route, because the
      //app will try to redeem the token again and render an error
      replace: true,
    })
  } else {
    //No forecast params, navigate to the dashboard home page
    navigate(DASHBOARD_NAVIGATION.FUNDED_PROGRESS, { replace: true })
  }
}

export default MagicLogin
