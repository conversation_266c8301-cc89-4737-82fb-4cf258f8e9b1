import { useEffect, useRef, useState } from 'react'
import { toast } from 'react-toastify'
import ToastMessage from '../../../common/components/ToastMessage'
import { useCustomNavigation } from '../../../common/hooks/useCustomNavigation'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { PRIVATE } from '../../../routes/Route'
import { ErrorStorage } from '../../CommonState.type'
import { useAccountService } from './useAccountService'

/**
 * This hook is used to manage the pin change workflow.
 * Handles element focus and pin change submission.
 */
export const usePinChange = (pinLength: number) => {
  const navigate = useCustomNavigation()
  const t = useTranslate()

  const { send } = useAccountService()

  const oldPinRefs = useRef<Array<HTMLInputElement>>([])
  const newPinRefs = useRef<Array<HTMLInputElement>>([])

  const [oldPin, setOldPin] = useState<Array<string>>([])
  const [error, setError] = useState<ErrorStorage>()

  // On successful pin change navigate to accounts page and clear pin fields
  const navigateToAccountPage = () =>
    navigate(PRIVATE.ACCOUNT, { replace: true })

  const onSuccessfulPinSubmit = () => {
    toast.success(
      <ToastMessage title={t('PIN_CHANGE.SUCCESS_MODAL_CONTENT')} />
    )

    navigateToAccountPage()
  }

  // On failed pin change clear all pin fields and focus the old pin field
  const onFailedPinSubmit = (error?: ErrorStorage) => {
    setError(error)
    setOldPin([])
    oldPinRefs.current[0]?.focus()
  }

  // On change of old pin, focus the new pin field once the old pin is filled
  const handleSetOldPin = (values: Array<string>) => {
    setOldPin(values)
    if (values?.length === pinLength) {
      newPinRefs.current[0]?.focus()
    }
  }

  const handleChangePin = (pin?: string) => {
    send({
      type: 'CHANGE_CURRENT_PIN',
      payload: {
        newPin: pin,
        oldPin: oldPin.join(''),
        successCallback: onSuccessfulPinSubmit,
        failureCallback: onFailedPinSubmit,
      },
    })
  }

  // Automatically focus on the old pin on load
  useEffect(() => {
    oldPinRefs.current[0]?.focus()
  }, [])

  return {
    oldPinRefs,
    newPinRefs,
    oldPin,
    setOldPin,
    error,
    handleSetOldPin,
    handleChangePin,
  }
}
