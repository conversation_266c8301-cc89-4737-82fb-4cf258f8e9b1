import { AsYouType } from 'libphonenumber-js'
import { useState } from 'react'
import { parsePhoneNum } from '../../../common/utils/UtilFunctions'
import { AUTH_CONSTANTS } from '../utils/consts'

/**
 * Controls the state of the visual phone number and the dropdown. The phone
 * number with the dial code is issue via the `onChange` prop as a string value
 */
export const usePhoneInput = ({
  onChange,
  value,
}: {
  onChange?: (value: string) => void
  value?: string
}) => {
  const [dialCode, setDialCode] = useState(
    parsePhoneNum(value ?? '')?.dialCode ?? AUTH_CONSTANTS.FALLBACK_DIAL_CODE
  )
  const [formattedPhoneNumber, setFormattedPhoneNumber] = useState(
    parsePhoneNum(value ?? '')?.formattedPhoneNumber
  )

  const onChangeDialCode = (dialCode?: string) => {
    if (dialCode) {
      setDialCode(dialCode)
      // Reset the text input field and issue an empty string
      // so if the user has changed the country code to wipe
      // the previous formatted phone number
      onChange?.('')
      setFormattedPhoneNumber('')
    }
  }

  /**
   * Sets the visual formatted state and issue a callback `onChange` with the
   * concat dial code and formatted phone number
   */
  const interOnChange = (phoneNumber: string) => {
    const fullNumber = `${dialCode}${phoneNumber}`
    setFormattedPhoneNumber(
      new AsYouType().input(fullNumber).replaceAll(dialCode ?? '', '')
    )

    onChange?.(phoneNumber ? fullNumber : '')
  }

  return {
    dialCode,
    setDialCode,
    onChangeDialCode,
    interOnChange,
    formattedPhoneNumber,
  }
}
