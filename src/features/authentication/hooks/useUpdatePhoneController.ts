import { useState } from 'react'
import { toast } from 'react-toastify'
import useBrowserStorage from '../../../common/hooks/useBrowserStorage'
import { useCustomNavigation } from '../../../common/hooks/useCustomNavigation'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { ACCOUNT_MENU } from '../../../routes/Route'
import { ErrorStorage } from '../../CommonState.type'
import { AUTH_CONSTANTS } from '../utils/consts'
import { useAccountService } from './useAccountService'

/**
 * Controls the phone number verification flow and provides session storage
 * functions to control the flow
 */
export const useUpdatePhoneController = () => {
  const t = useTranslate()
  const navigate = useCustomNavigation()

  const { addValueToStorage, removeValueFromStorage } = useBrowserStorage({
    key: AUTH_CONSTANTS.PHONE_ADDED_KEY,
    storageType: 'session',
  })

  const [apiError, setApiError] = useState<
    (ErrorStorage & { data: { expires_in_seconds: number } }) | undefined
  >(undefined)
  const [unverifiedPhoneNumber, setUnverifiedPhoneNumber] = useState<
    string | undefined
  >(undefined)
  const [readyForVerification, setReadyForVerification] = useState<
    string | undefined
  >(undefined)

  const { currentState, send } = useAccountService()

  const addPhoneNumber = () => {
    send({
      type: 'ADD_UNVERIFIED_PHONE',
      payload: {
        phone_number: unverifiedPhoneNumber,
        successCallback: () => {
          addValueToStorage(unverifiedPhoneNumber)
          setReadyForVerification(unverifiedPhoneNumber)
        },
        failureCallback: (error) =>
          setApiError(
            error as ErrorStorage & { data: { expires_in_seconds: number } }
          ),
      },
    })
  }

  /**
   * Issued when the phone number is fully verified, with the SMS code sent to
   * the user
   */
  const onSuccessfulVerification = () => {
    toast.success(t('PERSONAL_DETAILS.SUCCESS_EDITING_CONTENT'))

    navigate(ACCOUNT_MENU.PERSONAL_DETAILS)
  }

  return {
    t,
    addPhoneNumber,
    setUnverifiedPhoneNumber,
    readyForVerification,
    onSuccessfulVerification,
    setReadyForVerification,
    isLoading: currentState === 'ADDING_UNVERIFIED_PHONE',
    apiError,
    setApiError,
    removePersistedPhone: () =>
      removeValueFromStorage(AUTH_CONSTANTS.PHONE_ADDED_KEY),
  }
}
