import { useState } from 'react'
import { useParams } from 'react-router-dom'
import { ErrorStorage } from '../../CommonState.type'
import { useAccountService } from './useAccountService'

/**
 *
 * Provides the data needed to render a pin reset page
 */
export const usePinReset = () => {
  const { reset_token } = useParams()
  const { send } = useAccountService()

  // states
  const [error, setError] = useState<ErrorStorage>()
  const [shouldRenderSuccessModal, setShouldRenderSuccessModal] =
    useState(false)

  const onSuccessfulPinSubmit = () => setShouldRenderSuccessModal(true)

  const onFailedPinReset = (error?: ErrorStorage) => {
    // Show error modal only if there is an API error
    if (error) {
      setError(error)
    }
    // Otherwise it is just pins don't match error...
  }

  const handleResetPin = (newPin: string) => {
    send({
      type: 'RESET_PIN',
      payload: {
        pin: newPin,
        reset_token,
        successCallback: onSuccessfulPinSubmit,
        failureCallback: onFailedPinReset,
      },
    })
  }

  return {
    error,
    shouldRenderSuccessModal,
    handleResetPin,
    onFailedPinReset,
  }
}
