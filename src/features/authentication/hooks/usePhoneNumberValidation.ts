import { ValidationData } from '../../../common/types/CommonTypes.types'
import { useUserInfoValidation } from './useUserInfoValidation'

export const usePhoneNumValidation = () => {
  const { phoneNumberValidated, validatePhoneNumber } =
    useUserInfoValidation() as unknown as {
      phoneNumberValidated: ValidationData
      validatePhoneNumber: (value: string) => void
    }

  return {
    phoneNumberValidated,
    validatePhoneNumber,
  }
}
