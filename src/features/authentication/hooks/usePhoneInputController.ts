import useBrowserStorage from '../../../common/hooks/useBrowserStorage'
import { AUTH_CONSTANTS } from '../utils/consts'
import { useUserInfo } from './FormHooks'

/**
 * Controls the session storage to persist the unverified phone number during verification
 */
export const usePhoneInputController = () => {
  const { storedValue, removeValueFromStorage } = useBrowserStorage({
    key: AUTH_CONSTANTS.PHONE_ADDED_KEY,
    storageType: 'session',
  })

  const {
    phoneNumber: primaryPhoneNumber,
    setPhoneNumber: setPrimaryPhoneNumber,
  } = useUserInfo()

  return {
    unverifiedPhoneNumFromSs: storedValue,
    unverifiedPhone: primaryPhoneNumber,
    setUnverifiedPhone: setPrimaryPhoneNumber,
    removePersistedPhoneNumber: () =>
      removeValueFromStorage(AUTH_CONSTANTS.PHONE_ADDED_KEY),
  }
}
