import { useEffect, useState } from 'react'
import { useParams } from 'react-router-dom'
import { useCustomNavigation } from '../../../common/hooks/useCustomNavigation'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { generateUniqueId } from '../../../common/utils/UtilFunctions'
import { ErrorStorage } from '../../CommonState.type'
import { ScanRoutes } from '../types/FaceScan.type'
import { ScanParamToPageSetting } from '../utils/consts'
import { useAccountService } from './useAccountService'

/**
 * Handles the face scan and changes some page content depending on which type
 * of scan the user is doing
 */
export const useFaceScan = ({
  email,
  scanType,
}: {
  email?: string
  scanType?: ScanRoutes
}) => {
  const [scriptLoaded, setScriptLoaded] = useState(false)

  const [scanError, setScanError] = useState<undefined | string>(undefined)
  // Hooks
  const { send, context, currentState, states } = useAccountService()
  const { scan_type } = useParams() as {
    scan_type?: ScanRoutes
  }
  const navigate = useCustomNavigation()
  const t = useTranslate()

  // Default to auth scan always, no need to explicitly set it
  const pageSetting =
    ScanParamToPageSetting[scanType ?? scan_type ?? 'auth-scan']

  const startScan = (
    onSuccess?: (data: {
      idScanCompleted?: boolean
      enrollmentCompleted?: boolean
    }) => void,
    onFailure?: () => void
  ) => {
    send({
      type: 'START_FACE_SCAN',
      payload: {
        scanType: pageSetting.scanType,
        email: email ?? context.user_details?.email,
        successCallback: (data) =>
          onSuccess?.(
            data as {
              idScanCompleted?: boolean
              enrollmentCompleted?: boolean
            }
          ),
        failureCallback: (error) => {
          if (error) {
            if (
              !(error as ErrorStorage & { data: { isInit: boolean } })?.data
                ?.isInit
            ) {
              // Problem with facescan SDK not initializing
              setScanError('ERROR_GENERIC')
            } else {
              // Wrap the error so it renders something generic per scan type
              setScanError(pageSetting.scanError)
            }
          }
          onFailure?.()
        },
      },
    })
  }

  useEffect(() => {
    // Load the FaceTec SDK script dynamically
    const script = document.createElement('script')
    script.src = './facetec-web-sdk/FaceTecSDK.js/FaceTecSDK.js'
    script.nonce = generateUniqueId()
    // Not much of a security thing, maybe it is. Not fully sure
    script.async = true
    script.onload = () => setScriptLoaded(true)

    document.head.appendChild(script)

    return () => {
      document.head.removeChild(script)
    }
  }, [])

  return {
    t,
    navigate,
    scan_type,
    pageSetting,
    scanIsInProgress:
      currentState === states.ANON_FACE_SCAN_IN_PROGRESS ||
      currentState === states.AUTH_FACE_SCAN_IN_PROGRESS,
    startScan,
    scanError,
    scriptLoaded,
  }
}
