import { useState } from 'react'
import Layout from '../../../common/components/Layout'
import { useCustomNavigation } from '../../../common/hooks/useCustomNavigation'
import { useDeviceScreen } from '../../../common/hooks/useDeviceScreen'
import { useLocalization } from '../../../common/hooks/useLocalization'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { isLite } from '../../../config/lite'
import { PUBLIC } from '../../../routes/Route'
import { useAccountService } from '../../authentication/hooks/useAccountService'
import Home from '../components/Home'
import { useForecastParams } from '../hooks/useForecastFormParams'
import { ShowFormsType } from '../types/CalculatorForms.types'
import { CALCULATOR_FORMS } from '../utils/consts'
import AgeForm from './AgeForm'
import BlurredForecast from './BlurredForecast'
import ContributionForm from './ContributionForm'
import SexForm from './SexForm'

/**
 * Forms used for the "HOW IT WORKS" flow, where the user chooses params and
 * sends them for forecasting to the tontinator
 *
 * It manages the multi-step retirement income forecast onboarding flow.
 * It includes forms for collecting sex, age, and contribution information, then renders
 * a blurred forecast screen after all steps are completed.
 */
const CalculatorForms = () => {
  const { isMobileOrTablet } = useDeviceScreen()
  const t = useTranslate()
  const navigate = useCustomNavigation()
  const { isAuthenticated } = useAccountService()
  const { detectedCountry, isUSA } = useLocalization()

  const [showForms, setShowForms] = useState(
    CALCULATOR_FORMS.DEFAULT_SHOW_VALUES
  )

  const { incomeForecastParams, setIncomeForecastParams } = useForecastParams(
    detectedCountry.alpha3,
    isUSA
  )

  /** Updates form visibility by setting all form flags to false except the specified step. */
  const goToStep = (step: keyof ShowFormsType) => {
    setShowForms({
      sexForm: false,
      ageForm: false,
      contributionForm: false,
      infoForm: false,
      forecast: false,
      [step]: true,
    })
  }

  const returnToPreviousForm = () => {
    if (showForms.sexForm) navigate(PUBLIC.HOME)
    else if (showForms.ageForm) goToStep('sexForm')
    else if (showForms.contributionForm) goToStep('ageForm')
    else if (showForms.forecast) goToStep('contributionForm')
  }

  const formHeadline = t('ONBOARDING.SEE_HOW_IT_WORKS_HEADLINE')

  return (
    <>
      {!showForms.forecast && (
        <Layout
          navigateTo={PUBLIC.HOME}
          pageTitle={t('ONBOARDING.SEE_HOW_IT_WORKS_HEADLINE')}
          onClickAction={returnToPreviousForm}
          layoutVariant="sun-bg"
          containerWidth="small"
          containerMt="nomt"
          containerHeight={isLite ? 'lite-build' : 'auto'}
          hideDividerHeader
          hideMobileHeader={isLite}
        >
          {showForms.infoForm && <Home />}
          {showForms.sexForm && (
            <SexForm
              formData={incomeForecastParams}
              setFormData={setIncomeForecastParams}
              goToStep={goToStep}
              formHeaderText={
                isMobileOrTablet
                  ? t('ONBOARDING.STEP_1/3_PAGE_TITLE')
                  : formHeadline
              }
              progress={t('ONBOARDING.STEP_1/3_PAGE_TITLE')}
            />
          )}
          {showForms.ageForm && (
            <AgeForm
              formData={incomeForecastParams}
              setFormData={setIncomeForecastParams}
              goToStep={goToStep}
              formHeaderText={
                isMobileOrTablet
                  ? t('ONBOARDING.STEP_2/3_PAGE_TITLE')
                  : formHeadline
              }
              progress={t('ONBOARDING.STEP_2/3_PAGE_TITLE')}
            />
          )}
          {showForms.contributionForm && (
            <ContributionForm
              formData={incomeForecastParams}
              setFormData={setIncomeForecastParams}
              goToStep={goToStep}
              formHeaderText={
                isMobileOrTablet
                  ? t('ONBOARDING.STEP_3/3_PAGE_TITLE')
                  : formHeadline
              }
              progress={t('ONBOARDING.STEP_3/3_PAGE_TITLE')}
            />
          )}
        </Layout>
      )}

      {showForms.forecast && (
        <BlurredForecast
          isAuthenticated={isAuthenticated}
          incomeForecastParams={incomeForecastParams}
        />
      )}
    </>
  )
}

export default CalculatorForms
