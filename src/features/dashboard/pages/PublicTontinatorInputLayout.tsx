import Button from '../../../common/components/Button'
import Layout from '../../../common/components/Layout'
import { UI_TEST_ID } from '../../../common/constants/DataTestIDs'
import { useDeviceScreen } from '../../../common/hooks/useDeviceScreen'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { postMessageToParent } from '../../../common/hooks/useWindowPostMessage'
import ExtendedTontinatorInputs from '../components/ExtendedTontinatorInputs'
import PublicTontinatorInputs from '../components/PublicTontinatorInputs'
import PublicTontinatorInputsModal from '../components/PublicTontinatorInputsModal'
import style from '../style/BottomCtaLiteLayout.module.scss'
import { PublicTontinatorInputLayoutProps } from '../types/PublicTontinatorInputLayout.types'

/**
 * Renders a layout for the Public Tontinator
 * with sign in and sign up layout
 */
const PublicTontinatorInputLayout = ({
  isSliderPageOpen,
  incomeForecastParams,
  setIncomeForecastParams,
  comparison,
  blueForecastParams,
  setBlueForecastParams,
  yellowForecastParams,
  setYellowForecastParams,
  setOpenSliderPage,
}: PublicTontinatorInputLayoutProps) => {
  const { isMobileOrTablet } = useDeviceScreen()
  const t = useTranslate()

  const sharedProps = {
    isSliderPageOpen,
    incomeForecastParams,
    setIncomeForecastParams,
    comparison,
    blueForecastParams,
    setBlueForecastParams,
    yellowForecastParams,
    setYellowForecastParams,
    extendDefault: (
      <ExtendedTontinatorInputs
        incomeForecastParams={incomeForecastParams}
        setIncomeForecastParams={setIncomeForecastParams}
        showSex
        trackSex={[
          {
            trackId: 'tontinator_sex_male',
          },
          {
            trackId: 'tontinator_sex_female',
          },
        ]}
        trackInvStrategies={{
          trackId: 'tontinator_investment_strategy',
        }}
      />
    ),
    extendBlue: (
      <ExtendedTontinatorInputs
        incomeForecastParams={blueForecastParams}
        setIncomeForecastParams={setBlueForecastParams}
        trackInvStrategies={{
          trackId: 'plan1_investment_strategy',
        }}
      />
    ),
    extendYellow: (
      <ExtendedTontinatorInputs
        incomeForecastParams={yellowForecastParams}
        setIncomeForecastParams={setYellowForecastParams}
        trackInvStrategies={{
          trackId: 'plan2_investment_strategy',
        }}
      />
    ),
  }

  if (isMobileOrTablet) {
    if (isSliderPageOpen) {
      return (
        <Layout hideDividerHeader hideMobileHeader containerHeight="lite-build">
          <PublicTontinatorInputsModal
            tontinatorProps={sharedProps}
            isOpen={isSliderPageOpen}
          >
            <div className={style['bottom-cta-lite-layout__cta-to-slider']}>
              <Button
                variant="alternative"
                onClick={() => {
                  setOpenSliderPage((prev) => !prev)
                  postMessageToParent({
                    eventId: 'SCROLL_TO_TOP_TON',
                  })
                }}
                dataTestID={UI_TEST_ID.openSliderPageButton}
                trackActivity={{
                  trackId: 'tontinator_slider_page',
                }}
              >
                {t('CHECK_UPDATED_CHART')}
              </Button>
            </div>
          </PublicTontinatorInputsModal>
        </Layout>
      )
    }
  } else {
    // No mobile detected, just return the normal desktop layout
    return <PublicTontinatorInputs {...sharedProps} />
  }

  // In order to to break the layout
  return null
}

export default PublicTontinatorInputLayout
