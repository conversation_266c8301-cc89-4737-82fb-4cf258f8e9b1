@use '../../../common/style/abstracts/colors';
@use '../../../common/style/abstracts/mixins';
@use '../../../common/style/abstracts/variables';

/** @define nextPayout */
.nextPayout {
  @include mixins.flex-layout(column, center);
  padding: 1rem 1.25rem;
  background-color: colors.$white;
  width: 100%;
  border-radius: variables.$rounded;

  @media only screen and (max-width: variables.$mobile-devices) {
    padding: 0 1rem;
    padding-bottom: 1rem;
  }

  &--blue-faint {
    background-color: colors.$blue-faint;
  }

  &--gray-dirty {
    background-color: colors.$gray-faint;
  }

  &__circle-w-number {
    @include mixins.circle-with-number;

    &--selected {
      @include mixins.circle-with-number(
        $background-color: colors.$green,
        $color: colors.$white
      );
    }
  }

  &__amount-container {
    @include mixins.flex-layout(row, space-between, initial);
    width: 100%;
    font-size: variables.$font-size-m;
  }

  &__previous-amount {
    margin: 0;
  }

  &__next-amount-container {
    @include mixins.flex-layout;
  }

  &__info-amount {
    border-radius: variables.$rounded-pill;
    background-color: colors.$white-faint;
    font-size: variables.$font-size-xxs;
    text-align: center;
    padding: 0 0.3125rem;
  }

  &__next-amount {
    @include mixins.no-user-select;
    margin: 0;
    font-weight: variables.$font-bold;
  }

  &__prog-container {
    @include mixins.flex-layout(row, $justify-content: space-between);
    width: 100%;
  }

  &__start-circle {
    @include mixins.progress-circles(colors.$green, colors.$white, 15px);
  }

  &__end-circle {
    @include mixins.progress-circles(colors.$white, colors.$green, 15px);
    right: 0;

    &--payday {
      @include mixins.progress-circles(colors.$white, colors.$green, 15px);
      right: 0;
      background-color: colors.$green;
    }
  }

  &__dashes {
    width: 5px;
    border-radius: variables.$rounded-full;
    height: 5px;

    &--reached {
      background-color: colors.$green;
    }

    &--unreached {
      background-color: colors.$white;
      border: 1px solid colors.$green;
    }

    &--current {
      transform: scale(2);
    }
  }

  &__progress-bar {
    width: 100%;

    &[value] {
      @include mixins.payout-progress-bar('none');
    }

    &::-moz-progress-bar {
      @include mixins.payout-progress-bar;
    }

    &::-webkit-progress-bar {
      @include mixins.payout-progress-bar;
    }

    &::-webkit-progress-value {
      @include mixins.payout-progress-bar;
    }
  }
}
