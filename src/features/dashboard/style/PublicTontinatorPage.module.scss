@use '../../../common/style/abstracts/mixins';
@use '../../../common/style/abstracts/variables';
@use '../../../common/style/abstracts/colors';

.public-tontinator-page {
  @include mixins.flex-layout(column, none, none);

  &__chart-layout {
    width: 50%;
    // Makes sure the top callout is not cut
    margin-top: 25px;
  }



  &__tontinator-container {
    @include mixins.flex-layout($align-items: flex-start);
    gap: 3.125rem;
  }

  &__bottom-cta-container {
    @include mixins.flex-layout(column);
    gap: 1.25rem;
  }

  &__slider-group {
    @include mixins.no-user-select;
    margin-bottom: 0.625rem;
  }

  &__input-layout {
    padding-right: 1rem;
    width: 400px;

    &--full {
      height: 700px;
      overflow: scroll;


      @media only screen and (max-width: variables.$mobile-devices) {
        padding: 10px;
        margin-top: 70px;
        height: fit-content;
      }
    }
  }

  &__input-layout--no-group {
    max-width: 350px;
    flex-basis: 30%;
    margin-top: 50px;

    @media only screen and (max-width: variables.$mobile-devices) {
      margin-top: 20px;
    }

  }

  @media only screen and (max-width: variables.$mobile-devices) {
    gap: 5px;

    &__input-layout {
      max-width: 100%;
      width: 100%;
      padding: 1rem;
      padding-top: 2rem;
      height: fit-content;

      &>main {
        margin-bottom: 15px;

        &>section {
          margin-bottom: 15px;
        }
      }
    }

    &__input-layout--no-group {
      max-width: 100%;
    }

    &__chart-layout {
      width: 100%;
      // Makes sure the top call out on phones is not cutout but the screen
      margin-top: 15px;
    }
  }
}