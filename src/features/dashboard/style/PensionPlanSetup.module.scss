@use '../../../common/style/abstracts/colors';
@use '../../../common/style/abstracts/mixins';
@use '../../../common/style/abstracts/variables';

/* @define pension-plan-setup*/
.pension-plan-setup {
  padding: 25px 0 2px 2px;

  &--full {
    overflow-y: scroll;
    height: 700px;

    @media only screen and (max-width: variables.$mobile-devices) {
      height: auto;
    }
  }

  &__title {
    @include mixins.dashboard-subtitle;
  }

  &__section {
    border-radius: 5px;
    background-color: colors.$white;
    padding: 10px 15px 0 15px;
    padding-bottom: 50px;
    box-shadow: variables.$chart-box-shadow;
  }

  &__plan-suggestions {
    margin-top: variables.$mytt-dashboard-element-spacing;
    gap: 10px;
    @include mixins.flex-layout;
  }

  &__mobile-header {
    display: none;
    @include mixins.font-style($font-size: variables.$font-size-l);

    &-container {
      margin: 20px 0 !important;
    }
  }

  &__payout-container {
    @include mixins.flex-layout($flex-direction: column);
  }

  &__payout-center {
    max-width: 700px;
    @include mixins.flex-layout($flex-direction: column, $gap: 30px);
  }

  &__dashboard {
    &-divider {
      visibility: hidden;
      margin-bottom: 20px;
      margin-top: 10px;
    }

    &-graph-wrapper {
      width: 100%;
      background-color: colors.$white;
    }

    &-title {
      @include mixins.font-style($font-size: variables.$font-size-xlarge);
    }

    &-header-container {
      margin-top: 1.25rem !important;
      margin-bottom: 0.625rem !important;
    }
  }

  &__retirement-slider {
    @include mixins.flex-layout;
  }

  &__plan-amounts {
    width: 100%;
  }

  &__nav-buttons {
    margin-top: 0.625rem;
    position: static !important;
    margin-bottom: 1.25rem;
  }

  &__lower-section {
    @include mixins.flex-layout(column);
  }

  @media only screen and (max-width: variables.$mobile-devices) {
    &__payout-center {
      max-width: 100%;
    }

    &__mobile-header {
      text-align: center;
      display: block;
      @include mixins.font-style($font-size: variables.$font-size-l);
    }

    &__dashboard {
      &-payouts {
        display: none;
      }

      &-graph-wrapper {
        padding: 0.625rem 0.625rem 3.125rem 0.625rem;
      }

      &-title {
        @include mixins.font-style($font-size: variables.$font-size-l);
      }

      &-divider {
        display: none;
      }
    }

    &__plan-suggestions {
      gap: variables.$mytt-dashboard-element-spacing;
      @include mixins.flex-layout(column);
    }

    &__nav-buttons {
      position: initial;
      width: 100% !important;
    }
  }
}