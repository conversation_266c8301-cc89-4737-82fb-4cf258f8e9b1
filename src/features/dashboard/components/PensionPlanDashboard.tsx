import { lazy } from 'react'
import { track } from '../../../common/analytics/Analytics'
import { GraphEvent } from '../../../common/analytics/EventData'
import { EVENT_DESC } from '../../../common/analytics/EventDescription'
import Divider from '../../../common/components/Divider'
import TextError from '../../../common/components/TextError'
import { ANIMATION } from '../../../common/constants/Animations'
import { useLocalization } from '../../../common/hooks/useLocalization'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { IncomeForecastParams } from '../../../common/types/CommonTypes.types'
import { isLite } from '../../../config/lite'
import { useRequestIncomeForecast } from '../../../features/dashboard/hooks/useRequestIncomeForecast'
import { Styling } from '../../visualization/Styling'
import { StylingOptionsD3 } from '../../visualization/types/Visualization.types'
import { generateLegendItem } from '../../visualization/utils/D3DataUtils'
import style from '../style/PensionPlanSetup.module.scss'
import CompareIncomeStats from './CompareIncomeStats'
import CurrentAgeMobileMessage from './CurrentAgeMobileMessage'
import IncomeStats from './IncomeStats'
import type { PensionPlanDashboardProps } from './PensionPlanDashboard.types'

const TontinatorLineChart = lazy(
  () => import('../../../features/visualization/chart/TontinatorLineChart')
)

const X_AXIS_CSS_CLASS = 'x-axis-progress-graph-age'
const Y_AXIS_CSS_CLASS = 'y-axis-progress-graph-balance'
const CONTAINER_CSS_CLASS = 'progress-graph-container'

const MAIN_SVG_ID = 'pensionPlanSetup-svg-container'

const LINE_KEYS = {
  tontineLineKey: 'line-tontine',
}

const NEW_PLAN_COLOR = '#FFCC50'
const NEW_PLAN_FONT_COLOR = '#4D5155'
const CURRENT_PLAN_COLOR = '#2975bf'
const CURRENT_PLAN_FONT_COLOR = 'white'

const PLAN1_LINE_STYLE: StylingOptionsD3 = {
  stroke: '3px',
  fill: 'none',
  areaOpacity: 0.1,
  dashArray: '0, 0',
  showAnnotations: true,
  arrowHead: true,
  color: CURRENT_PLAN_COLOR,
  areaColor: CURRENT_PLAN_COLOR,
  textColor: CURRENT_PLAN_FONT_COLOR,
  inflationAreaOpacity: 0.1,
  transitionDuration:
    Styling.tontinatorLineChart.tontineLine.transitionDuration,
}

const PLAN2_LINE_STYLE: StylingOptionsD3 = {
  ...PLAN1_LINE_STYLE,
  color: NEW_PLAN_COLOR,
  areaColor: NEW_PLAN_COLOR,
  textColor: NEW_PLAN_FONT_COLOR,
}

const multipleLineStyle = [PLAN1_LINE_STYLE, PLAN2_LINE_STYLE]

const PensionPlanDashboard = ({
  className,
  dataToDraw,
}: PensionPlanDashboardProps) => {
  const t = useTranslate()
  const { formatTontinatorAmount, detectedCountry } = useLocalization()

  const { error, isLoading, forecastData } = useRequestIncomeForecast({
    incomeForecastParams: appendParamsIdToPlans({
      forecastArray: dataToDraw,
      paramsId: 'compare_plan',
    }),
  })

  const returnsForPlan1 =
    detectedCountry?.supportedInvestments?.[dataToDraw[0]?.strategy]

  const returnsForPlan2 =
    detectedCountry?.supportedInvestments?.[dataToDraw[1]?.strategy]

  const legendInitialData = [
    generateLegendItem({
      id: 'plan1',
      itemColor: CURRENT_PLAN_COLOR,
      text: t('PLAN1_INPUT_GROUP_LEGEND', {
        strategy: t(returnsForPlan1?.name ?? '', {
          return: returnsForPlan1?.rate?.toFixed(1),
        }),
      }),
      renderLine: true,
    }),
    generateLegendItem({
      id: 'plan2',
      itemColor: NEW_PLAN_COLOR,
      text: t('PLAN2_INPUT_GROUP_LEGEND', {
        strategy: t(returnsForPlan2?.name ?? '', {
          return: returnsForPlan2?.rate?.toFixed(1),
        }),
      }),
      renderLine: true,
    }),
  ]

  //Dynamically renders 1 or 2 legend items depending how many response items are received
  const legendData = Array.from(
    { length: forecastData?.length ?? 0 },
    (_, legendItemIndex) => {
      return legendInitialData[legendItemIndex]
    }
  )

  const formatPayoutAmount = (amount: number | undefined) => {
    if (forecastData && amount !== undefined) {
      return formatTontinatorAmount({
        amount,
        style: 'currency',
        currency: currency ?? '',
      })
    }
    return ''
  }

  /**
   * Returned from the tontinator API
   */
  const currency = forecastData?.[0]?.results?.currency

  return (
    <main
      className={`${className ?? ''} ${style[`pension-plan-setup${!isLite ? '--full' : ''}`]}`}
    >
      {error && <TextError errorText={error.response?.data?.message} />}
      <section className={style['pension-plan-setup__section']}>
        <TontinatorLineChart
          onHoverOrTapStart={() =>
            trackChartHover({ event: GraphEvent.hover_start })
          }
          isLoading={isLoading}
          forecastData={forecastData ?? []}
          formatter={(number) => {
            //Prevents from formatting `null` or `undefined` values
            if (number !== undefined && number !== null) {
              return formatPayoutAmount(number)
            }
            return ''
          }}
          numOfTicksForX={8}
          numOfTicksForY={8}
          xAxisCssClass={X_AXIS_CSS_CLASS}
          yAxisCssClass={Y_AXIS_CSS_CLASS}
          mainSVGContainerID={MAIN_SVG_ID}
          containerCssClass={CONTAINER_CSS_CLASS}
          axisDistanceFromGraph={12}
          tontineLineKey={LINE_KEYS.tontineLineKey}
          multipleTontineLineStyles={shouldRenderOneLine({
            forecastArray: dataToDraw,
            multipleLineStyle,
          })}
          toggles={{
            inflation: false,
            breakeven: false,
            percent: false,
            depositLine: false,
            annuityLine: false,
          }}
          showVerticalHoveringLine
          drawingAnimation={ANIMATION.jarWithCoins}
          showFocusCircleOnPath
          showHoveringMouseAnnotation
          legendData={legendData}
        />
      </section>

      <CurrentAgeMobileMessage
        paramsMode={dataToDraw[0]?.paramsMode}
        currentAge={dataToDraw[0]?.contributionAge?.age ?? 0}
      />

      <Divider className={style[`pension-plan-setup__dashboard-divider`]} />

      {shouldRenderPreviousAndCurrentPlan(dataToDraw) ? (
        <section className={style[`pension-plan-setup__payout-container`]}>
          <div className={style[`pension-plan-setup__payout-center`]}>
            <CompareIncomeStats
              plan1={{
                contributionAmount:
                  forecastData?.[0]?.stats?.total_contributions ?? 0,
                incomeAmount: forecastData?.[0]?.stats?.total_payouts ?? 0,
                incomePercentage:
                  forecastData?.[0]?.stats?.payout_percentage ?? 0,
                incomeStartAge: dataToDraw[0]?.retirementAge?.age ?? 0,
                isLoading,
                investmentStrategy: dataToDraw[0]?.strategy,
                currency: forecastData?.[0]?.results?.currency ?? '',
              }}
              plan2={{
                contributionAmount:
                  forecastData?.[1]?.stats?.total_contributions ?? 0,
                incomeAmount: forecastData?.[1]?.stats?.total_payouts ?? 0,
                incomePercentage:
                  forecastData?.[1]?.stats?.payout_percentage ?? 0,
                incomeStartAge: dataToDraw?.[1]?.retirementAge?.age ?? 0,
                isLoading,
                investmentStrategy: dataToDraw?.[1]?.strategy,
                currency: forecastData?.[1]?.results?.currency ?? '',
              }}
            />
          </div>
        </section>
      ) : (
        <div className={style[`pension-plan-setup__payout-container`]}>
          <div className={style[`pension-plan-setup__payout-center`]}>
            <IncomeStats
              variant="blue-faint"
              isLoading={isLoading}
              contributionAmount={
                forecastData?.[0]?.stats?.total_contributions ?? 0
              }
              contributionLabel={t('INCOME_STAT_CONTRIBUTION_LABEL', {
                incomeStartAge: dataToDraw[0]?.retirementAge?.age,
              })}
              incomeAmount={forecastData?.[0]?.stats?.total_payouts ?? 0}
              incomeLabel={t('FORECAST_PAGE.PAYOUTS_BY_100_LABEL')}
              incomePercentage={
                forecastData?.[0]?.stats?.payout_percentage ?? 0
              }
              currency={currency ?? ''}
            />
          </div>
        </div>
      )}
    </main>
  )
}

/**
 * Checks if the forecast response array contains more than one forecast data
 * object
 */
const shouldRenderPreviousAndCurrentPlan = (
  forecastResponse: IncomeForecastParams[]
) => forecastResponse?.length > 1

const trackChartHover = ({ event }: { event: GraphEvent }) => {
  void track({
    event,
    properties: {
      object_id: 'compare_plan',
      description: EVENT_DESC.chartHoverOrTap,
    },
  })
}

/**
 * Appends an paramsId to the forecast params. The id is used for analytic purposes
 */
const appendParamsIdToPlans = ({
  forecastArray,
  paramsId,
}: {
  forecastArray: IncomeForecastParams[]
  paramsId: string
}) => {
  if (forecastArray && forecastArray?.length > 1) {
    return [
      {
        ...forecastArray[0],
        paramsId,
      },
      {
        ...forecastArray[1],
        paramsId,
      },
    ]
  }

  return forecastArray
}

/**
 * If the forecast params are both equal renders only one line in the chart to
 * prevent overlap so the styling does not look bad
 */
const shouldRenderOneLine = ({
  forecastArray,
  multipleLineStyle,
}: {
  forecastArray: IncomeForecastParams[]
  multipleLineStyle: StylingOptionsD3[]
}) => {
  if (forecastArray && forecastArray?.length > 1) {
    // Handles {age,month} when generating a hash
    const flattenObject = (obj: Record<string, unknown>) => {
      const flattened: unknown[] = []
      for (const value of Object.values(obj)) {
        if (value && typeof value === 'object' && !Array.isArray(value)) {
          flattened.push(...Object.values(value))
        } else {
          flattened.push(value)
        }
      }
      return flattened.join('')
    }

    const hashParams1 = flattenObject(
      forecastArray[0] as unknown as Record<string, unknown>
    )
    const hashParams2 = flattenObject(
      forecastArray[1] as unknown as Record<string, unknown>
    )

    if (hashParams1 === hashParams2) {
      return [
        multipleLineStyle[0],
        {
          ...multipleLineStyle[1],
          color: 'transparent',
          textColor: 'transparent',
          areaColor: 'transparent',
        },
      ]
    }
  }
  return multipleLineStyle
}

export default PensionPlanDashboard
