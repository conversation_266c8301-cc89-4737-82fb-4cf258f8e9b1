import { track } from '../../../common/analytics/Analytics'
import { InputFieldEvent } from '../../../common/analytics/EventData'
import Dropdown from '../../../common/components/Dropdown'
import { ASSET } from '../../../common/constants/Assets'
import { useLocalization } from '../../../common/hooks/useLocalization'
import { useTranslate } from '../../../common/hooks/useTranslate'
import {
  InvestmentStrategy,
  InvestmentStrategyId,
} from '../../../common/types/CommonTypes.types'
import { InvStrategiesDropdownProps } from '../types/Dashboard.types'

/**
 * Renders investment strategies dropdown using the `investment-strategies.json`
 * as a data source
 */
const InvStrategiesDropdown = ({
  value,
  onChange,
  label,
  errorMessage,
  validatorFunction,
  placeholder,
  readOnly,
  dataTestID,
  className,
  trackActivity,
}: InvStrategiesDropdownProps) => {
  const t = useTranslate()
  const { detectedCountry } = useLocalization()

  if (detectedCountry?.supportedInvestments) {
    return (
      <Dropdown
        options={Object?.entries(detectedCountry.supportedInvestments)
          .map(([key, value]) => {
            return {
              ...value,
              id: key,
              name: t(value.name, {
                return: value.rate.toFixed(1),
              }),
              icon: ASSET?.[key as keyof typeof ASSET],
              secondaryIcon: ASSET?.[value.riskLevel as keyof typeof ASSET],
            }
          })
          // Sorted by risk level
          .sort((curr, prev) =>
            (curr?.riskLevel ?? '') < (prev?.riskLevel ?? '') ? -1 : 1
          )}
        value={value as unknown as InvestmentStrategy}
        onChange={(value) => {
          onChange(value as unknown as InvestmentStrategyId)
          void track({
            event: InputFieldEvent.selected,
            properties: {
              object_id: trackActivity?.trackId,
              object_value: value,
            },
          })
        }}
        itemKey={{ displayKey: 'name', valueOnChange: 'id' }}
        searchBy={['name', 'id']}
        label={label}
        errorMessage={errorMessage}
        validatorFunction={validatorFunction}
        placeholder={placeholder}
        readOnly={readOnly}
        dataTestID={dataTestID}
        className={className}
        optional
      />
    )
  }

  return null
}

export default InvStrategiesDropdown
