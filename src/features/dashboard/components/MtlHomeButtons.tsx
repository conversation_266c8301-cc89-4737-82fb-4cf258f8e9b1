import { toast } from 'react-toastify'
import { track } from '../../../common/analytics/Analytics'
import { PensionPlan } from '../../../common/analytics/EventData'
import { EVENT_DESC } from '../../../common/analytics/EventDescription'
import Button from '../../../common/components/Button'
import ButtonAndClickableText from '../../../common/components/ButtonAndClickableText'
import Divider from '../../../common/components/Divider'
import Icon from '../../../common/components/Icon'
import ToastMessage from '../../../common/components/ToastMessage'
import { ASSET } from '../../../common/constants/Assets'
import { UI_TEST_ID } from '../../../common/constants/DataTestIDs'
import { useDeviceScreen } from '../../../common/hooks/useDeviceScreen'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { debounce, deepEqual } from '../../../common/utils/UtilFunctions'
import { isLite } from '../../../config/lite'
import { useAccountService } from '../../authentication/hooks/useAccountService'
import { useLiteAuth } from '../../authentication/hooks/useLiteAuth'
import style from '../style/BottomCtaLiteLayout.module.scss'
import { PublicTontinatorHomeButtonsProps } from '../types/PublicTontinatorHomeBtns.types'

/**
 * Renders the bottom CTA buttons for the Public Tontinator homepage
 * Displays different buttons based on authentication status
 */
const PublicTontinatorHomeButtons = ({
  onSeeOtherScenarios,
  onCompareChoices,
  incomeForecastParams,
  onClickSignUpButton,
  setIsOpenSignInModal,
}: PublicTontinatorHomeButtonsProps) => {
  const t = useTranslate()
  const { isAuth } = useLiteAuth()
  const { send, currentState, context } = useAccountService()
  const { isMobileOrTablet } = useDeviceScreen()

  const submitNewPlan = debounce(() => {
    send({
      type: 'UPDATE_MTL_DRAFT_PLAN',
      payload: {
        draftPensionPlan: incomeForecastParams,
        successCallback: () => {
          toast.success(
            <ToastMessage
              title={t('PERSONAL_DETAILS.SUCCESS_EDITING_CONTENT')}
            />
          )
          void track({
            event: PensionPlan.lite_plan_updated,
            properties: {
              object_value: incomeForecastParams,
              object_id: 'tontinator',
            },
          })
        },
        failureCallback: () =>
          toast.error(<ToastMessage title={t('ERROR_GENERIC')} />),
      },
    })
  }, 1_000)

  return (
    <>
      <Icon
        fileName={ASSET.tontiRaisedHands}
        className={
          style[`bottom-cta-lite-layout__mascot${isAuth ? '--auth' : ''}`]
        }
      />
      <Icon
        fileName={ASSET.tontiHappy}
        className={
          style[`bottom-cta-lite-layout__mascot-1${isAuth ? '--auth' : ''}`]
        }
      />

      {isMobileOrTablet && (
        <Button
          dataTestID={UI_TEST_ID.openSliderPageButton}
          onClick={onSeeOtherScenarios}
          variant="alternative"
        >
          {t('CHECK_OTHER_SCENARIOS')}
        </Button>
      )}

      <Button
        dataTestID={UI_TEST_ID.openComparePlanButton}
        onClick={onCompareChoices}
        variant="blue"
      >
        {t('BUTTON_TO_COMPARE')}
      </Button>

      {isMobileOrTablet && <Divider />}

      {isAuth && (
        <Button
          loading={currentState === 'UPDATING_MTL_PENSION_PLAN'}
          onClick={submitNewPlan}
          trackActivity={{
            trackId: 'tontinator_update_lite_plan',
          }}
          disabled={
            // Already has this plan
            // no need to spam requests
            deepEqual(
              incomeForecastParams,
              context.liteData?.pensionPlan as object
            )
          }
          dataTestID={UI_TEST_ID.updateLitePlanBtn}
        >
          {t('TONTINATOR_UPDATE_PLAN')}
        </Button>
      )}

      {!isAuth && isLite && (
        <ButtonAndClickableText
          buttonVariant="primary--animated"
          buttonLabel={t('BUTTON_TO_PRE_REG')}
          buttonOnClick={onClickSignUpButton}
          textLabel={t('REFERRAL_BANNER_SIGN_IN')}
          textOnClick={() => setIsOpenSignInModal?.(true)}
          buttonDataTestID={UI_TEST_ID.registerButton}
          textDataTestID={UI_TEST_ID.loginBtnDesktop}
          buttonTrackActivity={{
            trackId: 'tontinator_open_register_screen',
            eventDescription: EVENT_DESC.tontinatorOpenRegisterScreen,
          }}
          textTrackActivity={{
            trackId: 'tontinator_login_text',
            eventDescription: EVENT_DESC.tontinatorLoginText,
          }}
        />
      )}
    </>
  )
}

export default PublicTontinatorHomeButtons
