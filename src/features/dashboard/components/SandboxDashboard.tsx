import Button from '../../../common/components/Button'
import { useDeviceScreen } from '../../../common/hooks/useDeviceScreen'
import { useTranslate } from '../../../common/hooks/useTranslate'
import sandboxStyle from '../style/SandboxDashboard.module.scss'
import tontinatorInputsModalStyle from '../style/TontinatorInputsModal.module.scss'
import { SandboxDashboardProps } from '../types/SandboxDashboard.types'
import InvStrategiesDropdown from './InvStrategiesDropdown'
import PensionPlanDashboard from './PensionPlanDashboard'
import PublicTontinatorInputs from './PublicTontinatorInputs'
import PublicTontinatorInputsModal from './PublicTontinatorInputsModal'
import TontinatorDashboard from './TontinatorDashboard'

const SandboxDashboard = ({
  incomeForecastParams,
  setIncomeForecastParams,
  blueForecastParams,
  setBlueForecastParams,
  yellowForecastParams,
  setYellowForecastParams,
  isCompareOpen,
  isParamsOpen,
  setIsParamsOpen,
}: SandboxDashboardProps) => {
  const t = useTranslate()

  const { isMobileOrTablet } = useDeviceScreen()

  const inputsParams = {
    blueForecastParams: blueForecastParams,
    comparison: isCompareOpen,
    incomeForecastParams: incomeForecastParams,
    setIncomeForecastParams: setIncomeForecastParams,
    setBlueForecastParams: setBlueForecastParams,
    setYellowForecastParams: setYellowForecastParams,
    yellowForecastParams: yellowForecastParams,
    extendDefault: (
      <InvStrategiesDropdown
        value={incomeForecastParams?.strategy}
        onChange={(strategy) => {
          setIncomeForecastParams({
            ...incomeForecastParams,
            strategy,
          })
        }}
        label={'Investment Strategy'}
        trackActivity={{
          trackId: 'tontinator_investment_strategy',
        }}
        className={sandboxStyle['sandbox-dashboard__investment-strategy']}
      />
    ),
    extendBlue: (
      <InvStrategiesDropdown
        value={blueForecastParams?.strategy}
        onChange={(strategy) => {
          setBlueForecastParams({
            ...blueForecastParams,
            strategy,
          })
        }}
        label={'Investment Strategy'}
        trackActivity={{
          trackId: 'tontinator_investment_strategy',
        }}
      />
    ),
    extendYellow: (
      <InvStrategiesDropdown
        value={yellowForecastParams?.strategy}
        onChange={(strategy) => {
          setYellowForecastParams({
            ...yellowForecastParams,
            strategy,
          })
        }}
        label={'Investment Strategy'}
        trackActivity={{
          trackId: 'tontinator_investment_strategy',
        }}
      />
    ),
    propsForDefaultLayout: {
      hideRetirementSliders: true,
      showRetirementScheduler: true,
    },
    propsForBlueLayout: {
      hideRetirementSliders: true,
      showRetirementScheduler: true,
    },
    propsForYellowLayout: {
      hideRetirementSliders: true,
      showRetirementScheduler: true,
    },
  }

  return (
    <section className={sandboxStyle['sandbox-dashboard__dashboards']}>
      {isCompareOpen ? (
        <PensionPlanDashboard
          dataToDraw={[blueForecastParams, yellowForecastParams]}
        />
      ) : (
        <TontinatorDashboard incomeForecastParams={incomeForecastParams} />
      )}
      {!isMobileOrTablet && <PublicTontinatorInputs {...inputsParams} />}
      {isMobileOrTablet && isParamsOpen && (
        <PublicTontinatorInputsModal
          isOpen={isParamsOpen}
          tontinatorProps={inputsParams}
          className={tontinatorInputsModalStyle['tontinator-inputs-modal']}
        >
          <div
            className={
              tontinatorInputsModalStyle[
                'tontinator-inputs-modal__buttons-wrapper'
              ]
            }
          >
            <Button
              onClick={() => setIsParamsOpen(false)}
              variant="alternative"
            >
              {t('CHECK_UPDATED_CHART')}
            </Button>
          </div>
        </PublicTontinatorInputsModal>
      )}
    </section>
  )
}

export default SandboxDashboard
