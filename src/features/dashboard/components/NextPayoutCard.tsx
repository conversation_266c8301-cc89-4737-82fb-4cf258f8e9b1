import { Trans } from 'react-i18next'
import ExtendedContentCard from '../../../common/components/ExtendedContentCard'
import { ASSET } from '../../../common/constants/Assets'
import { useLocalization } from '../../../common/hooks/useLocalization'
import { useBankingService } from '../../banking/hooks/useBankingService'
import { DASHBOARD_CONSTANTS } from '../utils/consts'
import { daysUntilNextPayout } from '../utils/UtilFunctions'
import NextPayout from './NextPayout'

/**
 *  Renders a next payout card with a title and a progress bar until
 * the next payout has completed, a spinner is rendered while the component is
 * fetching data
 */
const NextPayoutCard = ({ className }: { className?: string }) => {
  const { error, nextPayoutInfo, loading } = usePayoutData()
  const { formatAmount } = useLocalization()

  //Render an empty div for now if there is an error,
  //there needs to be further discussion about this behavior
  if (error) {
    return null
  }

  if (!loading && nextPayoutInfo?.next) {
    return (
      <div className={className}>
        <ExtendedContentCard
          headerImage={ASSET.iconnextpayoutcard}
          title={
            <Trans
              i18nKey={
                daysUntilNextPayout().remaining === 0
                  ? 'NEXT.PAYOUT.TODAY'
                  : 'NEXT_PAYOUT_COUNTDOWN'
              }
              values={{
                day: daysUntilNextPayout().remaining,
              }}
            />
          }
          // hoverDisabled
          autoExpand
          variant={'gray-dirty'}
          headerImageSize="large"
          extendedCardVariant="payout"
          expandClickDisabled
        >
          <NextPayout
            maxProgress={daysUntilNextPayout()?.currentDaysInMonth}
            currentProgress={daysUntilNextPayout()?.todayDate}
            previousAmount={
              nextPayoutInfo?.currency
                ? (formatAmount({
                    amount: nextPayoutInfo?.previous,
                    currency: nextPayoutInfo?.currency,
                    style: 'currency',
                    digits: DASHBOARD_CONSTANTS.CURRENCY_DIGITS_FORMATTING,
                    notation: 'compact',
                  })?.formattedAmountWithSymbol ?? '')
                : ''
            }
            nextAmount={
              nextPayoutInfo?.currency
                ? (formatAmount({
                    amount: nextPayoutInfo?.next,
                    currency: nextPayoutInfo?.currency,
                    style: 'currency',
                    notation: 'compact',
                    digits: DASHBOARD_CONSTANTS.CURRENCY_DIGITS_FORMATTING,
                  })?.formattedAmountWithSymbol ?? '')
                : ''
            }
            variant={'gray-dirty'}
          />
        </ExtendedContentCard>
      </div>
    )
  }

  return null
}

/*
 * Uses the bankContext data to render next payout card
 */
const usePayoutData = () => {
  const { bankContext, currentBankState, states } = useBankingService()

  return {
    loading: currentBankState === states?.FETCHING_BANK_INFO,
    nextPayoutInfo: {
      previous: Math.abs(
        bankContext?.bankingInfo?.nextPayout?.[0]?.gross?.amount ?? 0
      ),
      next: Math.abs(
        bankContext?.bankingInfo?.nextPayout?.[1]?.gross?.amount ?? 0
      ),
      currency: bankContext?.bankingInfo?.nextPayout?.[1]?.gross?.currency,
    },
    error: bankContext?.bankingInfoError,
  }
}

export default NextPayoutCard
