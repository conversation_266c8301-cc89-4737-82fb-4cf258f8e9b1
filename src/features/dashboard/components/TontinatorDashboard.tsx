import { GraphEvent } from '../../../common/analytics/EventData'
import BannerMessage from '../../../common/components/BannerMessage'
import ErrorBoundaryAndSuspense from '../../../common/components/ErrorBoundaryAndSuspense'
import TextError from '../../../common/components/TextError'
import { ANIMATION } from '../../../common/constants/Animations'
import { useLocalization } from '../../../common/hooks/useLocalization'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { isLite } from '../../../config/lite'
import TontinatorLineChart from '../../visualization/chart/TontinatorLineChart'
import { useChartLegend } from '../../visualization/hooks/useChartLegend'
import { Styling } from '../../visualization/Styling'
import { useRequestIncomeForecast } from '../hooks/useRequestIncomeForecast'
import {
  trackChartHover,
  useGraphToggles,
} from '../hooks/useTontinatorDashboard'
import style from '../style/TontinatorDashboard.module.scss'
import { TontinatorDashboardProps } from '../types/TontinatorDashboard.types'
import {
  CONTAINER_CSS_CLASS,
  LINE_KEYS,
  MAIN_SVG_ID,
  X_AXIS_CSS_CLASS,
  Y_AXIS_CSS_CLASS,
} from '../utils/consts'
import CompareIncomeStats from './CompareIncomeStats'
import CurrentAgeMobileMessage from './CurrentAgeMobileMessage'
import GraphSwitches from './GraphSwitches'

const TontinatorDashboard = ({
  incomeForecastParams,
  svgID,
}: TontinatorDashboardProps) => {
  const t = useTranslate()
  const { formatTontinatorAmount, detectedCountry } = useLocalization()
  const returnsForProduct =
    detectedCountry?.supportedInvestments?.[
      incomeForecastParams?.strategy
    ]?.rate?.toFixed(1) ?? '-'

  const { depositLine, annuityLine, lineStyleVisibility, generateLegend } =
    useChartLegend({
      //If all lines should be rendered initially
      renderTontineLine: true,
      renderAnnuityLine: false,
      renderDepositLine: false,
    })

  const {
    percentage,
    handlePercentage,
    breakeven,
    handleBreakeven,
    inflation,
    handleInflation,
    bannerMessageKey,
  } = useGraphToggles()

  const { error, isLoading, forecastData } = useRequestIncomeForecast({
    // Tontinator only has one set of params, and this will not change any time
    // soon, so the params passed in are wrapped in an array
    incomeForecastParams: [
      {
        ...incomeForecastParams,
        writeDraftPlan: false,
        paramsId: 'tontinator',
      },
    ],
  })

  /**
   * Returned from the tontinator API
   */
  const currency = forecastData?.[0]?.results?.currency ?? 'USD'

  const formatPayoutAmount = (amount: number) => {
    if (forecastData && amount) {
      return formatTontinatorAmount({
        amount,
        style: percentage ? 'percent' : 'currency',
        currency: currency ?? 'USD',
      })
    }
    return ''
  }

  return (
    <main className={style[`tontinatorDashboard${!isLite ? '--full' : ''}`]}>
      {error && (
        <TextError
          errorText={t('ERROR_BAD_FORECAST')}
          className={style['tontinatorDashboard__error-text']}
        />
      )}
      <section className={`${style['tontinatorDashboard__section']}`}>
        <TontinatorLineChart
          isLoading={isLoading}
          onHoverOrTapStart={() =>
            trackChartHover({ event: GraphEvent.hover_start })
          }
          formatter={formatPayoutAmount}
          numOfTicksForX={8}
          numOfTicksForY={8}
          xAxisCssClass={`${X_AXIS_CSS_CLASS}${svgID ? svgID : ''}`}
          yAxisCssClass={`${Y_AXIS_CSS_CLASS}${svgID ? svgID : ''}`}
          mainSVGContainerID={svgID ?? MAIN_SVG_ID}
          forecastData={forecastData ?? []}
          containerCssClass={CONTAINER_CSS_CLASS}
          axisDistanceFromGraph={12}
          tontineLineKey={`${LINE_KEYS.tontineLineKey}${svgID ? svgID : ''}`}
          tontineLineStyle={lineStyleVisibility({
            styling: Styling,
            isVisible: true,
            isInflationEnabled: inflation,
            lineName: 'tontineLine',
          })}
          bankDepositKey={`${LINE_KEYS.bankDepositKey}${svgID ? svgID : ''}`}
          bankDepositsStyle={lineStyleVisibility({
            styling: Styling,
            isVisible: depositLine,
            isInflationEnabled: inflation,
            lineName: 'bankDepositLine',
          })}
          fixedAnnuityLineKey={`${LINE_KEYS.fixedAnnuityLineKey}${svgID ? svgID : ''}`}
          fixedAnnuityStyle={lineStyleVisibility({
            styling: Styling,
            isVisible: annuityLine,
            isInflationEnabled: inflation,
            lineName: 'annuityLine',
          })}
          toggles={{
            inflation,
            breakeven,
            percent: percentage,
            depositLine,
            annuityLine,
          }}
          showVerticalHoveringLine
          drawingAnimation={ANIMATION.jarWithCoins}
          showFocusCircleOnPath
          showHoveringMouseAnnotation
          legendData={
            returnsForProduct
              ? generateLegend({
                  returnsForProduct,
                  productKey: incomeForecastParams?.paramsMode ?? 'TTF',
                  strategyKey: incomeForecastParams?.strategy ?? 'FII',
                })
              : []
          }
        />
        <GraphSwitches
          breakevenLabel={t(
            breakeven ? 'AGE.TO.BREAKEVEN.HIDE' : 'AGE.TO.BREAKEVEN.SHOW'
          )}
          inflationLabel={t(
            inflation
              ? 'FORECAST_PAGE.GRAPH_SWITCH_INFLATION_LABEL_HIDE'
              : 'FORECAST_PAGE.GRAPH_SWITCH_INFLATION_LABEL',
            {
              inflationPercent: isLoading
                ? // Prevents the user seeing NaN for percent value while it is
                  // being fetched from the backend
                  '-'
                : (forecastData?.[0]?.results?.annual_inflation_rate ?? 0) *
                  100,
            }
          )}
          percentage={percentage}
          handlePercentage={handlePercentage}
          breakeven={breakeven}
          handleBreakeven={handleBreakeven}
          inflation={inflation}
          handleInflation={handleInflation}
          togglesVariant="button"
          currency={currency ?? 'USD'}
        />
        {bannerMessageKey && (
          <article className={style['tontinatorDashboard__banner-container']}>
            <BannerMessage hideIcon variant="info">
              {t(bannerMessageKey)}
            </BannerMessage>
          </article>
        )}
      </section>

      <CurrentAgeMobileMessage
        paramsMode={incomeForecastParams?.paramsMode}
        currentAge={incomeForecastParams?.contributionAge?.age ?? 0}
      />

      <main className={style[`tontinatorDashboard__payout-container`]}>
        <section className={style[`tontinatorDashboard__payout-center`]}>
          <ErrorBoundaryAndSuspense hideNavButton>
            <CompareIncomeStats
              plan1={{
                currency,
                contributionAmount:
                  forecastData?.[0]?.stats?.total_contributions ?? 0,
                incomeAmount: forecastData?.[0]?.stats?.total_payouts ?? 0,
                incomePercentage:
                  forecastData?.[0]?.stats?.payout_percentage ?? 0,
                incomeStartAge:
                  typeof incomeForecastParams?.retirementAge === 'object'
                    ? incomeForecastParams.retirementAge.age
                    : (incomeForecastParams?.retirementAge ??
                      incomeForecastParams?.retirementAge ??
                      0),
                isLoading,
                investmentStrategy: incomeForecastParams?.strategy,
              }}
            />
          </ErrorBoundaryAndSuspense>
        </section>
      </main>
    </main>
  )
}

export default TontinatorDashboard
