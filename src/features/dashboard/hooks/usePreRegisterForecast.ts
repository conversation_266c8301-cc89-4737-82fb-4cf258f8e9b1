import { useEffect, useState } from 'react'
import { useLocalization } from '../../../common/hooks/useLocalization'
import {
  IncomeForecastParams,
  InvestmentDetail,
  InvestmentStrategyId,
  SexType,
  TontinatorParamsMode,
} from '../../../common/types/CommonTypes.types'
import { TontinatorUIParams } from '../../../common/types/SupportedCountries.types'
import { useAccountService } from '../../authentication/hooks/useAccountService'
import { useLiteAuth } from '../../authentication/hooks/useLiteAuth'
import {
  InvestmentDetails,
  UserDetails,
} from '../../authentication/types/AuthMachineTypes.type'
import { AgeMonth } from '../../CommonState.type'
import { PreRegisterForecast } from '../types/PreRegisterForecast.types'
import { DASHBOARD_CONSTANTS } from '../utils/consts'

/**
 * Increments the income start age in order to show the difference
 * if the income starts with this variable added
 */
const incrementToShowDifference = 5

const overriddenUsaMaxRetAge = 73

/**
 * Controls the state of the income forecast params and comparison against them
 * `tontinatorParams` are thresholds!
 */
export const useForecastParamsState = ({
  defaultParams,
  tontinatorParams,
}: PreRegisterForecast) => {
  const { litePensionPlan } = useLiteAuth()
  const { detectedCountry } = useLocalization()
  const {
    context: { user_details },
  } = useAccountService()

  // DEFAULT VALUE FOR ALL THE ONBOARDING FORMS
  const [incomeForecastParams, _setIncomeForecastParams] =
    useState(defaultParams)

  // Comparison params
  const [blueForecastParams, _setBlueForecastParams] =
    useState(incomeForecastParams)
  const [yellowForecastParams, _setYellowForecastParams] = useState({
    ...incomeForecastParams,
    ...(modifyParamsForComparison({
      retirementAge: incomeForecastParams?.retirementAge ?? {
        age: 0,
        month: 0,
      },
      targetValue:
        defaultParams.countryOfResidence === 'USA'
          ? overriddenUsaMaxRetAge
          : (tontinatorParams?.maxRetirementAge?.age ?? 0),
      targetIncrement: incrementToShowDifference,
      strategies: detectedCountry.supportedInvestments as InvestmentDetails,
      strategy: incomeForecastParams?.strategy,
    }) as IncomeForecastParams),
  })

  useEffect(() => {
    // HACK: Workaround until we have pension plans
    // in order for sandbox tontinator page not to error out
    if (user_details?.date_of_birth) {
      _setIncomeForecastParams((prev) => ({
        ...prev,
        contributionAge: user_details?.age as AgeMonth,
      }))
    }
  }, [user_details?.date_of_birth])

  useEffect(() => {
    // Init the UI with the lite plan from backend
    if (litePensionPlan) {
      _setIncomeForecastParams(litePensionPlan)
      _setBlueForecastParams(litePensionPlan)
      _setYellowForecastParams(litePensionPlan)
    }
  }, [litePensionPlan, tontinatorParams?.maxRetirementAge?.age])

  const setBlueForecastParams = (params: IncomeForecastParams) => {
    _setBlueForecastParams((prevParams) => {
      // Makes sure the retirement age on the comparison is not below the
      // contribution age
      const cappedRetAge = Math.max(
        params.retirementAge?.age ?? 0,
        incomeForecastParams?.contributionAge?.age
      )

      return {
        ...prevParams,
        ...params,
        retirementAge: {
          month: params.retirementAge?.month ?? 0,
          age: cappedRetAge,
        },
      }
    })
  }

  const setYellowForecastParams = (params: IncomeForecastParams) => {
    _setYellowForecastParams((prevParams) => {
      // Makes sure the retirement age on the comparison is not below the
      // contribution age
      const cappedRetAge = Math.max(
        params.retirementAge?.age ?? 0,
        incomeForecastParams?.contributionAge?.age
      )

      return {
        ...prevParams,
        ...params,
        retirementAge: {
          month: params.retirementAge?.month ?? 0,
          age: cappedRetAge,
        },
      }
    })
  }

  /**
   * @param params - Passed in from yellow or blue in order to
   * override the main state with the user's picked params
   *
   * Syncs both plans params with the income forecast params
   * this gets executed when the user goes to the comparison page
   * any params can be modified before going there
   */
  const setIncomeForecastParams = (params: IncomeForecastParams) => {
    _setIncomeForecastParams(params)

    // Sets the Plan 1 comparison params when going to comparison page
    _setBlueForecastParams(params)
    // Sets the Plan 2 comparison params when going to comparison page
    _setYellowForecastParams((prevParams) => {
      const targetValue =
        params.countryOfResidence === 'USA'
          ? overriddenUsaMaxRetAge
          : (tontinatorParams?.maxRetirementAge?.age ?? 0)

      return {
        ...prevParams,
        ...params,
        ...modifyParamsForComparison({
          retirementAge: params?.retirementAge ?? { age: 0, month: 0 },
          targetValue,
          targetIncrement: incrementToShowDifference,
          strategies:
            detectedCountry?.supportedInvestments ?? ({} as InvestmentDetails),
          strategy: params?.strategy,
        }),
      }
    })
  }

  return {
    incomeForecastParams,
    setIncomeForecastParams,
    blueForecastParams,
    setBlueForecastParams,
    yellowForecastParams,
    setYellowForecastParams,
  }
}

/**
 * +5 years retirement age (up to the maximum of 73, if aged less than 73), examples:
 * - Tontinator/Plan 1 retirement age 60 -> comparison plan 2 retirement age 65
 * - Tontinator/Plan 1 retirement age 65 -> comparison plan 2 retirement age 70
 * - Tontinator/Plan 1 retirement age 70 -> comparison plan 2 retirement age 73
 * - Tontinator/Plan 1 retirement age 73 -> comparison plan 2 retirement age 73
 *
 * If aged 73 or more skip modification
 */
const modifyYellowRetirementAge = ({
  targetValue,
  retirementAge,
  targetIncrement,
}: {
  targetValue: number
  retirementAge: number
  targetIncrement: number
}) => {
  if (
    typeof targetValue !== 'number' ||
    typeof retirementAge !== 'number' ||
    typeof targetIncrement !== 'number'
  ) {
    throw new TypeError(
      `modifyYellowRetirementAge expects numbers as arguments,got 
          targetValue:${typeof targetValue}, 
          retirementAge:${typeof retirementAge}, 
          targetIncrement:${typeof targetIncrement}`
    )
  }

  if (retirementAge > targetValue) {
    return retirementAge
  }

  const distance = targetValue - retirementAge
  if (distance >= targetIncrement) {
    return retirementAge + targetIncrement
  }

  return Math.min(retirementAge + distance, targetValue)
}

/**
 * Chooses default params to init the tontinator with, if params are passed via
 * query string, they are used, otherwise the default params are used. All 7
 * params need to be passed in order for the params to be used
 */
export const chooseDefaultParams = ({
  tontinatorParams,
  urlSearchParams,
  supportedCountry,
  isAuthenticated,
  userDetails,
}: {
  tontinatorParams: TontinatorUIParams
  urlSearchParams?: URLSearchParams
  supportedCountry: {
    alpha3?: string
    supportedInvestments?: InvestmentDetails
  }
  isAuthenticated?: boolean
  userDetails?: UserDetails
}) => {
  const defaultParamsFromUi = {
    contributionAge: isAuthenticated
      ? (userDetails?.age as AgeMonth)
      : tontinatorParams?.defaultCurrentAgeSlider,
    retirementAge: isAuthenticated
      ? (tontinatorParams?.minRetirementAge as AgeMonth)
      : tontinatorParams?.defaultRetirementAgeSlider,
    sex: tontinatorParams?.defaultSex,
    oneTimeContribution: tontinatorParams?.defaultOneTimeSliderValue,
    countryOfResidence: supportedCountry?.alpha3 ?? '',
    monthlyContribution: 0,
    strategy: (() => {
      if (
        supportedCountry?.supportedInvestments &&
        Object?.keys(supportedCountry?.supportedInvestments).includes('FII')
      ) {
        return 'FII'
      }
      // Third option, because it is present in every country
      return 'BOL'
    })() as InvestmentStrategyId,
    paramsMode: 'TTF' as TontinatorParamsMode,
  }
  // more than 2 because the first param is the country and it needs to be
  // changed independently for easier changing
  if (urlSearchParams && urlSearchParams.size > 0 && tontinatorParams) {
    const {
      maxCurrentAge,
      maxRetirementAge,
      minCurrentAge,
      minRetirementAge,
      defaultCurrentAgeSlider,
      defaultRetirementAgeSlider,
      defaultOneTimeSliderValue,
      defaultSex,
      defaultMonthlySliderValue,
    } = tontinatorParams

    const monthlyContribution = (() => {
      const qMonthly = urlSearchParams.get(
        DASHBOARD_CONSTANTS.MONTHLY_DEPOSIT_Q
      )
      if (qMonthly) {
        const paramMonthlyContribution = Number.parseInt(qMonthly)
        if (isNaN(paramMonthlyContribution)) {
          return defaultMonthlySliderValue
        }

        return paramMonthlyContribution
      }
      return defaultMonthlySliderValue
    })()

    const contributionAge = (() => {
      const qCurrentAge = urlSearchParams.get(DASHBOARD_CONSTANTS.CURRENT_AGE_Q)
      if (qCurrentAge) {
        const paramContributionAge = Number.parseInt(qCurrentAge)
        if (isNaN(paramContributionAge)) {
          return defaultCurrentAgeSlider
        }
        return {
          age: Math.min(
            Math.max(paramContributionAge, minCurrentAge?.age ?? 0),
            maxCurrentAge?.age ?? 0
          ),
          month: 0,
        }
      }
      return defaultCurrentAgeSlider
    })()

    const retirementAge = (() => {
      const qRetirementAge = urlSearchParams.get(
        DASHBOARD_CONSTANTS.RETIREMENT_AGE_Q
      )
      if (qRetirementAge) {
        const paramRetirementAge = Number.parseInt(qRetirementAge)
        if (isNaN(paramRetirementAge)) {
          return defaultRetirementAgeSlider
        }
        return {
          age: Math.min(
            Math.max(paramRetirementAge, minRetirementAge?.age ?? 0),
            maxRetirementAge?.age ?? 0
          ),
          month: 0,
        }
      }
      return defaultRetirementAgeSlider
    })()

    const sex = (() => {
      const qSex = urlSearchParams.get(DASHBOARD_CONSTANTS.SEX_Q)
      if (qSex) {
        if (!['Male', 'Female'].includes(qSex)) {
          return defaultSex
        }
        return qSex as SexType
      }
      return defaultSex
    })()

    const oneTimeContribution = (() => {
      const qOneTime = urlSearchParams.get(
        DASHBOARD_CONSTANTS.ONE_TIME_DEPOSIT_Q
      )
      if (qOneTime) {
        const paramOneTimeContribution = Number.parseInt(qOneTime)
        if (isNaN(paramOneTimeContribution)) {
          return defaultOneTimeSliderValue
        }
        return paramOneTimeContribution
      }
      return defaultOneTimeSliderValue
    })()

    const strategy = (() => {
      const qStrategy = urlSearchParams.get(
        DASHBOARD_CONSTANTS.INVESTMENT_STRAT
      )
      if (qStrategy) {
        if (!['FII', 'BOL', 'VBI', 'BTC', 'XAU'].includes(qStrategy)) {
          return defaultParamsFromUi.strategy
        }
        return qStrategy
      }
      return defaultParamsFromUi.strategy
    })() as InvestmentStrategyId

    return {
      ...defaultParamsFromUi,
      contributionAge,
      retirementAge,
      sex,
      oneTimeContribution,
      countryOfResidence:
        urlSearchParams.get(DASHBOARD_CONSTANTS.COUNTRY_Q) ||
        defaultParamsFromUi.countryOfResidence,
      monthlyContribution,
      strategy,
      paramsMode: 'TTF' as TontinatorParamsMode,
    }
  }

  return defaultParamsFromUi
}

/**
 * Returns a different lowest risk investment strategy if there are more than 1,
 * for comparison purposes, if there are no strategies, returns the selected
 */
export const modifyParamsForComparison = ({
  strategy,
  strategies,
  targetValue,
  retirementAge,
  targetIncrement,
}: {
  strategy: InvestmentStrategyId
  strategies: InvestmentDetails
  targetValue: number
  retirementAge: AgeMonth
  targetIncrement: number
}) => {
  let modifiedStrategy = strategy
  let modifiedRetirementAge = retirementAge?.age

  // If there is one strategy modify retirement age in order
  // to compare plan difference
  if (strategies && Object?.keys(strategies).length === 1) {
    modifiedRetirementAge = modifyYellowRetirementAge({
      retirementAge: retirementAge?.age,
      targetValue,
      targetIncrement,
    })
  }

  if (strategies && Object?.keys(strategies).length > 1) {
    // Initialize the minimum difference and the closest number
    let minDiff = Number.POSITIVE_INFINITY
    const excludedPickedStrategy = Object.fromEntries(
      Object.entries(strategies).filter(
        ([key]) => ![strategy].includes(key as InvestmentStrategyId)
      )
    ) as Record<InvestmentStrategyId, InvestmentDetail>

    Object?.entries(excludedPickedStrategy).forEach((strategyDetails) => {
      // Rate difference between the picked strategy and the current strategy
      // array EXCLUDING the picked strategy
      const diff = Math.abs(
        strategyDetails[1]?.rate - strategies[strategy]?.rate
      )
      // If the difference is smaller than the current minimum difference
      if (diff < minDiff) {
        // Update the minimum difference and the closest number
        minDiff = diff
        modifiedStrategy = strategyDetails[0] as InvestmentStrategyId
      }
    })
  }

  return {
    retirementAge: { age: modifiedRetirementAge, month: retirementAge?.month },
    strategy: modifiedStrategy,
  }
}
