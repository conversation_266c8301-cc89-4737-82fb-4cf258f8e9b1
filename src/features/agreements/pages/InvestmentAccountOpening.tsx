import { lazy, useState } from 'react'
import GeneralError from '../../../common/components/GeneralError'
import LottieAnimation from '../../../common/components/LottieAnimation'
import NavigationButtons from '../../../common/components/NavigationButtons'
import PageLayout from '../../../common/components/PageLayout'
import { ANIMATION } from '../../../common/constants/Animations'
import { pickProperty } from '../../../common/utils/UtilFunctions'
import { useLegalMachine } from '../hooks/useLegalMachine'
import { RenderPageStates } from '../types/CommonLegalTypes.types'
import {
  AgreementDataForInvestmentAccount,
  InvestmentAccountFormType,
  TontineProduct,
} from '../types/LegalMachineTypes.types'
import LegalExplainerPage from './LegalExplainerPage'

const AgreementPage = lazy(() => import('./AgreementPage'))

const DEFAULT_FLOW_STATE: RenderPageStates = {
  entryPage: true,
  investmentForm: false,
  errorPage: false,
  statusPage: false,
  agreementPage: false,
  signPdf: false,
}

const investmentAccountOpeningFormType = 'InvestmentAccountOpening'

/**
 * Starts and controls the investment account opening flow. The whole flow is
 * dynamic and fetched from the backend.
 */
const InvestmentAccountOpening = () => {
  const { shouldRenderPage, setShouldRenderPage, legalContext } =
    useInitializeFlow({
      // The order of the response is followed by the order the form types
      // are passed
      formTypes: [investmentAccountOpeningFormType],
      product: 'TontineIRA',
    })

  if (shouldRenderPage?.errorPage) {
    return <GeneralError />
  }

  if (shouldRenderPage?.entryPage) {
    return <LegalExplainerPage setShouldRenderPage={setShouldRenderPage} />
  }

  if (shouldRenderPage.agreementPage && legalContext?.agreement) {
    return (
      <AgreementPage
        setShouldRenderPage={setShouldRenderPage}
        agreementData={
          pickProperty(
            legalContext.agreement
            // Changing the order here also changes the order in which the
            // agreement flow starts from
          ) as AgreementDataForInvestmentAccount
        }
      />
    )
  }

  if (shouldRenderPage.investmentForm) {
    //TODO: Waiting on requirements
    return (
      <PageLayout>
        <h1>Under construction</h1>
        <NavigationButtons
          hideActionButton
          firstButtonLabel={'Back to initial page'}
          onClickFirst={() => setShouldRenderPage(DEFAULT_FLOW_STATE)}
        />
      </PageLayout>
    )
  }

  return (
    <PageLayout containerWidth="small">
      <LottieAnimation animationName={ANIMATION.jarWithCoins} autoplay loop />
    </PageLayout>
  )
}

/**
 * Initializes the investment account opening flow, and fetches all the needed
 * data from the backend in order to initialize the whole flow
 */
const useInitializeFlow = ({
  product,
  formTypes,
}: {
  product: TontineProduct
  formTypes: Array<InvestmentAccountFormType>
}) => {
  const { currentLegalState, legalContext } = useLegalMachine()

  const [shouldRenderPage, setShouldRenderPage] =
    useState<RenderPageStates>(DEFAULT_FLOW_STATE)

  return {
    isLoading: currentLegalState === 'FETCHING_AGREEMENT',
    shouldRenderPage,
    setShouldRenderPage,
    legalContext,
    product,
    formTypes,
  }
}

export default InvestmentAccountOpening
