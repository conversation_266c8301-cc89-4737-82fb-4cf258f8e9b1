import { useEffect } from 'react'
import Icon from '../../../common/components/Icon'
import Layout from '../../../common/components/Layout'
import NavigationButtons from '../../../common/components/NavigationButtons'
import { ASSET } from '../../../common/constants/Assets'
import useBrowserStorage from '../../../common/hooks/useBrowserStorage'
import { useCustomNavigation } from '../../../common/hooks/useCustomNavigation'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { COMMON_CONSTANTS } from '../../../common/utils/consts'
import { ACCOUNT_MENU } from '../../../routes/Route'
import style from '../style/LegalExplainerPage.module.scss'
import { LegalExplainerPageProps } from '../types/LegalExplainerPage.types'

/**
 * Explainer page for user's legal obligations. For now it is not generic, but
 * can be turned into a generic page
 */
const LegalExplainerPage = ({
  setShouldRenderPage,
}: LegalExplainerPageProps) => {
  const { addValueToStorage } = useBrowserStorage({
    key: COMMON_CONSTANTS.VISITED_EXPLAINER_PAGE,
  })

  const t = useTranslate()
  const navigate = useCustomNavigation()

  useEffect(() => {
    addValueToStorage(true)
  }, [addValueToStorage])

  return (
    <Layout
      className={style.legalExplainerPage}
      hideDividerHeader
      navigateTo={ACCOUNT_MENU.FUND_PLAN_MENU}
      bottomSection={
        <NavigationButtons
          onClickFirst={() => navigate(ACCOUNT_MENU.FUND_PLAN_MENU)}
          secondButtonLabel={t('BUTTON_LABEL.NEXT')}
          onClickSecond={() =>
            setShouldRenderPage({
              agreementPage: true,
            })
          }
        />
      }
    >
      <section className={style[`legalExplainerPage__inner-container`]}>
        <Icon
          fileName={ASSET.step3}
          className={style[`legalExplainerPage__top-img`]}
        />
        <h2 className={style[`legalExplainerPage__title`]}>
          {t('ACC.OPENING.INTRO.HEADER')}
        </h2>
        <p className={style[`legalExplainerPage__content`]}>
          {t('ACC.OPENING.INTRO.BODY.TEXT')}
        </p>
      </section>
    </Layout>
  )
}

export default LegalExplainerPage
