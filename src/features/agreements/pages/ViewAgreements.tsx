import { useState } from 'react'
import Card from '../../../common/components/card/Card'
import Layout from '../../../common/components/Layout'
import NavigationButtons from '../../../common/components/NavigationButtons'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { COMMON_CONSTANTS } from '../../../common/utils/consts'
import { convertDateToClientLocale } from '../../../common/utils/UtilFunctions'
import { PUBLIC } from '../../../routes/Route'
import AgreementContent from '../components/AgreementContent'
import { useLegalMachine } from '../hooks/useLegalMachine'

/**
 * Renders agreements that the user has or has not agreed to
 */
const ViewAgreements = () => {
  const t = useTranslate()

  const {
    agreementsChecklist,
    viewAgreementContent,
    setViewAgreement,
    shouldRenderAgreementContents,
  } = useViewAgreements()

  const renderNavigationButtons = () => {
    if (shouldRenderAgreementContents) {
      return (
        <NavigationButtons
          hideActionButton
          // Back to checklist
          onClickFirst={() => setViewAgreement(undefined)}
        />
      )
    }
    return null
  }

  return (
    <Layout
      containerWidth={shouldRenderAgreementContents ? undefined : 'small'}
      hideBottomSectionDivider={!shouldRenderAgreementContents}
      textNextToHeader={
        viewAgreementContent?.userAgreed
          ? convertDateToClientLocale(
              viewAgreementContent?.userAgreed,
              COMMON_CONSTANTS.CLOSURE_ACCOUNT_DATE_FORMAT
            )?.formattedLocaleDate
          : ''
      }
      // When an agreement contents are rendered we want width 100%
      className={shouldRenderAgreementContents ? 'agreement-page' : ''}
      pageTitle={
        shouldRenderAgreementContents
          ? viewAgreementContent?.title
          : t('ACCOUNT.MENU_ITEM_TERMS_OF_CONDITIONS')
      }
      bottomSection={renderNavigationButtons()}
      navigateTo={PUBLIC.GO_TO_PREV_PAGE}
    >
      {shouldRenderAgreementContents ? (
        <AgreementContent
          backgroundColor="white"
          agreementContent={viewAgreementContent?.text ?? ''}
          agreementImage={viewAgreementContent?.image ?? ''}
          agreementHeading={viewAgreementContent?.heading ?? ''}
          readOnly
        />
      ) : (
        agreementsChecklist
      )}
    </Layout>
  )
}

/**
 * Returns an array of agreements from the legal context and renders a
 * checklist. If an agreement is selected it will render the contents of the
 * selected agreement
 */
const useViewAgreements = () => {
  const [viewAgreement, setViewAgreement] = useState<undefined | number>(
    undefined
  )
  const { legalContext } = useLegalMachine()
  const agreementsArray = Object.values(legalContext?.agreement ?? {})

  const agreementsChecklist = agreementsArray.map((agreement, index) => {
    return (
      <article key={`${agreement.title}${index}`}>
        <Card
          title={agreement.title}
          variant="gray-dirty"
          alert={agreement?.userAgreed ? 'completed' : 'warn'}
          showArrow
          interactEnabled
          onClick={() => setViewAgreement(index)}
        />
        <br />
      </article>
    )
  })

  const shouldRenderAgreementContents = viewAgreement !== undefined

  return {
    agreementsChecklist,
    setViewAgreement,
    shouldRenderAgreementContents,
    viewAgreementContent: shouldRenderAgreementContents
      ? agreementsArray?.[viewAgreement]
      : undefined,
  }
}

export default ViewAgreements
