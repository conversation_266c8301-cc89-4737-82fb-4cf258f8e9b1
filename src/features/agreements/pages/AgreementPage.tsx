import { Dispatch, SetStateAction, useState } from 'react'
import Layout from '../../../common/components/Layout'
import NavigationButtons from '../../../common/components/NavigationButtons'
import { useTranslate } from '../../../common/hooks/useTranslate'
import { COMMON_CONSTANTS } from '../../../common/utils/consts'
import {
  debounce,
  writeToConsoleAndIssueAlert,
} from '../../../common/utils/UtilFunctions'
import { DASHBOARD_CONSTANTS } from '../../dashboard/utils/consts'
import AgreementContent from '../components/AgreementContent'
import { useFormPageController } from '../hooks/useFormPageController'
import { useLegalMachine } from '../hooks/useLegalMachine'
import { RenderPageStates } from '../types/CommonLegalTypes.types'
import {
  AgreementData,
  AgreementDataForInvestmentAccount,
  AgreementForInvestmentAccount,
} from '../types/LegalMachineTypes.types'

type AgreementPageProps = {
  setShouldRenderPage: Dispatch<SetStateAction<RenderPageStates>>
  agreementData: AgreementDataForInvestmentAccount
}

/**
 * Renders an agreement pages with the given agreement data.
 * The user can sign an agreement by checking the checkbox.
 */
const AgreementPage = ({
  setShouldRenderPage,
  agreementData,
}: AgreementPageProps) => {
  const t = useTranslate()

  const { currentLegalState, states } = useLegalMachine()

  const { goToNextAgreement, handleBackButton, formPageNumber } =
    useAgreementsPage({
      setShouldRenderPage,
      agreementCount: Object.entries(agreementData).length,
    })

  const { agreementID, agreementContent, checkboxInitialState } =
    parseAgreementToIDAndData({
      agreementData,
      // Renders one agreement on page from the agreements data
      agreementToRenderIndex: formPageNumber,
    })

  const [agreementCheckboxes, setAgreementCheckboxes] =
    useState(checkboxInitialState)

  const { handleSigning, errorSign } = useSubmitSignedAgreement({
    goToNextAgreement,
  })

  return (
    <Layout
      onClickAction={handleBackButton}
      pageTitle={agreementContent?.title ?? ''}
      bottomSection={
        <NavigationButtons
          onClickFirst={handleBackButton}
          secondButtonLabel={t('BUTTON_LABEL.NEXT')}
          onClickSecond={goToNextAgreement}
          // Prevents the user from going to the next page
          // if the previous agreement is not submitted successfully
          disabledSecond={
            currentLegalState !== states.SIGNING_AGREEMENT &&
            !checkboxInitialState?.[agreementID ?? '']
          }
          secondButtonLoading={currentLegalState === states.SIGNING_AGREEMENT}
        />
      }
    >
      <AgreementContent
        backgroundColor="white"
        agreementSigningError={errorSign?.[agreementID ?? '']}
        agreementHeading={agreementContent?.heading ?? ''}
        agreementImage={agreementContent?.image ?? ''}
        agreementContent={agreementContent?.text ?? ''}
        agreementSigned={
          agreementID ? agreementCheckboxes?.[agreementID] : false
        }
        onAgreed={
          // Do not include the handler if the agreement has been signed
          agreementID && agreementCheckboxes?.[agreementID]
            ? undefined
            : (agreementData) => {
                // Makes the API request to sign the
                handleSigning({
                  agreementType: agreementID,
                  agreementSignedData: agreementData as {
                    checkboxChecked: boolean
                    agreementData: AgreementData
                  },
                })

                // Sets the checkbox state to true to visually indicate to the user
                // that the agreement has been signed
                setAgreementCheckboxes((previousState) => {
                  if (previousState) {
                    return {
                      ...previousState,
                      [agreementID ?? '']: agreementData.checkboxChecked,
                    }
                  }
                  // If previous state is somehow undefined, no idea how that can
                  // happen, so we return the default state
                  return previousState
                })
              }
        }
        agreementData={agreementData}
      />
    </Layout>
  )
}

/**
 * Submits the signed agreement and updates the state of the checkbox if the
 * agreement signing has failed
 */
const useSubmitSignedAgreement = ({
  goToNextAgreement,
}: {
  goToNextAgreement?: () => void
}) => {
  const [errorSign, setErrorSign] = useState<
    { [key: string]: string } | undefined
  >()

  const { sendLegalEvent, currentLegalState, states } = useLegalMachine()

  const handleSigning = ({
    agreementType,
    agreementSignedData,
  }: {
    agreementType?: AgreementForInvestmentAccount
    agreementSignedData?: {
      checkboxChecked: boolean
      agreementData: AgreementData
    }
  }) => {
    const debouncedSendLegal = debounce(
      sendLegalEvent,
      DASHBOARD_CONSTANTS.DEBOUNCE_SLIDERS_TIME
    )

    // Reset state error
    setErrorSign(undefined)

    if (agreementSignedData) {
      debouncedSendLegal({
        type: 'SIGN_AGREEMENT',
        payload: {
          agreementType,
          signedAgreementData: {
            checkboxChecked: agreementSignedData?.checkboxChecked,
            signedAgreementContents: agreementSignedData?.agreementData,
          },
          successCallback: () => {
            goToNextAgreement?.()
          },
          failureCallback: () => {
            // Can be modified, for now don't have any design...
            setErrorSign({ [agreementType as string]: agreementType as string })
          },
        },
      })
    }
  }

  return {
    handleSigning,
    isLoading: currentLegalState === states.SIGNING_AGREEMENT,
    errorSign,
  }
}

/**
 * Controls the agreements flow. Saves the agreement page that the user has left from
 */
const useAgreementsPage = ({
  setShouldRenderPage,
  agreementCount,
}: {
  setShouldRenderPage: Dispatch<SetStateAction<RenderPageStates>>
  agreementCount: number
}) => {
  const { formPageNumber, forwardOnePage, backOnePage } = useFormPageController(
    {
      key: COMMON_CONSTANTS.FORM_AGREEMENTS_KEY,
      pageCount: agreementCount,
    }
  )

  const goToNextAgreement = () => {
    if (formPageNumber + 1 !== agreementCount) {
      forwardOnePage()
    } else {
      setShouldRenderPage({
        investmentForm: true,
      })
    }
  }
  const handleBackButton = () => {
    //If on first form page then the user can go back to the entry page if they
    //wish so
    if (formPageNumber === 0) {
      setShouldRenderPage({ entryPage: true })
    } else {
      backOnePage()
    }
  }

  return {
    handleBackButton,
    goToNextAgreement,
    formPageNumber,
  }
}

/**
 * `agreementIndex` is necessary to determine on which page the user is on
 * Parses agreement content and data into one object with different data types
 * to be used in the UI.
 */
const parseAgreementToIDAndData = ({
  agreementData,
  agreementToRenderIndex,
}: {
  agreementData: AgreementDataForInvestmentAccount
  agreementToRenderIndex: number
}) => {
  try {
    let checkboxInitialState = {} as {
      [key in AgreementForInvestmentAccount]: boolean
    }
    let agreementIDs: Array<AgreementForInvestmentAccount> = []

    Object.entries(agreementData).forEach((agreement) => {
      checkboxInitialState = {
        ...checkboxInitialState,
        [agreement[0]]: Boolean(agreement[1]?.userAgreed),
      }

      agreementIDs = [
        ...agreementIDs,
        // Not sure what TS is smoking, it is clearly defined that the agreement
        // type is not a string but of type AgreementForInvestmentAccount, so
        // for some reason need to assert
        agreement[0] as AgreementForInvestmentAccount,
      ]
    })

    const agreementID = agreementIDs[agreementToRenderIndex]
    const agreementContent = agreementData[agreementID]

    return {
      agreementID,
      agreementData: agreementData?.[agreementID],
      agreementContent,
      checkboxInitialState,
    }
  } catch (error) {
    writeToConsoleAndIssueAlert({
      error,
      errorMessage: 'Parser catastrophic failure',
      skipRethrow: true,
    })
  }

  return {}
}

export default AgreementPage
