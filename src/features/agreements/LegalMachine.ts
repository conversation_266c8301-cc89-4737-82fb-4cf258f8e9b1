import { assign, setup } from 'xstate'
import { legalPromiseToPromiseActor, promiseActorInput } from '../StateUtils'
import { storeAgreements, updateAgreement } from './LegalMachineActions'
import {
  fetchAgreement,
  getFormAsPdf,
  signAgreement,
} from './LegalMachineServices'
import {
  LegalMachineContext,
  LegalMachineEvent,
} from './types/LegalMachineTypes.types'

/**
 * Handles all of the legal parts of the app. Like signing agreements, fetching
 * legal forms and sending the data to the server
 */
export const legalMachine = setup({
  types: {
    context: {} as LegalMachineContext,
    events: {} as LegalMachineEvent,
  },
  actors: {
    fetchAgreement: legalPromiseToPromiseActor(fetchAgreement),
    signAgreement: legalPromiseToPromiseActor(signAgreement),
    getFormAsPdf: legalPromiseToPromiseActor(getFormAsPdf),
  },
  actions: {
    storeAgreements: assign(storeAgreements),
    updateAgreement: assign(updateAgreement),
  },
}).createMachine({
  context: {
    agreement: undefined,
  },
  id: 'LegalMachine',
  initial: 'IDLE',
  states: {
    IDLE: {
      on: {
        FETCH_AGREEMENT: {
          target: 'FETCHING_AGREEMENT',
        },
        SIGN_AGREEMENT: {
          target: 'SIGNING_AGREEMENT',
        },
      },
    },

    SIGNING_AGREEMENT: {
      invoke: {
        src: 'signAgreement',
        id: 'signAgreementID',
        input: promiseActorInput,
        onDone: [
          {
            target: 'IDLE',
            actions: [{ type: 'updateAgreement' }],
          },
        ],
        onError: [
          {
            target: 'IDLE',
          },
        ],
      },
    },

    FETCHING_AGREEMENT: {
      invoke: {
        src: 'fetchAgreement',
        id: 'fetchAgreementID',
        input: promiseActorInput,
        onDone: [
          {
            target: 'IDLE',
            actions: [{ type: 'storeAgreements' }],
          },
        ],
        onError: [
          {
            target: 'IDLE',
          },
        ],
      },
    },
  },
})
