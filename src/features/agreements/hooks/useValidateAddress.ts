import { useState } from 'react'
import { regex } from '../../../common/constants/Regex'
import {
  StateData,
  ValidationData,
} from '../../../common/types/CommonTypes.types'
import { formatPostalCode } from '../../../common/utils/Formatters'
import { validateInputWithError } from '../../../common/utils/UtilFunctions'
import { isZipInState } from '../../authentication/utils/AddressValidators'

//Validation regex exclusive to this hook Supports ZIP+4 codes in the USA
const addressMinLength = 3
const cityLength = 2

/**
 * Validates address information, for now the implementation is very generic,
 * only validates if field is empty
 */
export const useValidateAddress = ({
  initialStreetAddressOneValidated,
  initialCityValidated,
  initialPostalCodeValidated,
  initialStreetAddressTwoValidated,
  initialUsaStateValidated,
}: {
  initialStreetAddressOneValidated?: ValidationData
  initialCityValidated?: ValidationData
  initialPostalCodeValidated?: ValidationData
  initialStreetAddressTwoValidated?: ValidationData
  initialUsaStateValidated?: ValidationData
} = {}) => {
  const [streetAddressLineOneValidated, setStreetAddressLineOneValidated] =
    useState<ValidationData | undefined>(initialStreetAddressOneValidated)

  const [streetAddressLineTwoValidated, setStreetAddressLineTwoValidated] =
    useState<ValidationData | undefined>(initialStreetAddressTwoValidated)

  const [cityValidated, setCityValidated] = useState<
    ValidationData | undefined
  >(initialCityValidated)

  const [postalCodeValidated, setPostalCodeValidated] = useState<
    ValidationData | undefined
  >(initialPostalCodeValidated)

  const [usaStateValidated, _setUsaStateValidated] = useState<
    ValidationData | undefined
  >(initialUsaStateValidated)

  /**
   * Validates user postal code by matching it against a selected USA state,
   * checking the format with regex and checking if the field is empty
   */
  const validateUserPostalCode = (
    postalCode?: string,
    state?: StateData,
    silentValidate?: boolean,
    postalCodeRegex?: RegExp,
    zipCodeFormatter?: typeof formatPostalCode,
    postalCodeLength?: [number, number]
  ) => {
    if (typeof state === 'string') {
      validateInputWithError({
        input: postalCode,
        emptyInputErrorI18nKey: 'ERROR_EMPTY_INPUT',
        setStateAction: setPostalCodeValidated,
      })
    } else {
      validateInputWithError({
        input: postalCode,
        emptyInputErrorI18nKey: 'ERROR_EMPTY_INPUT',
        validateFormat: (input: string) => {
          //Need to format it for regex, otherwise it will be considered invalid
          const formattedInput =
            zipCodeFormatter?.(input, postalCodeLength ?? [0, 0]) ?? ''

          return postalCodeRegex ? !postalCodeRegex.test(formattedInput) : true
        },
        invalidInputErrorI18nKey: 'ERROR_INVALID_ZIP_CODE',
        extendedValidator: (input: string) => {
          if (state) {
            const [firstPart, secondPart] = input.split('-')
            const combinedInput = firstPart + secondPart

            if (state.iso_code.startsWith('US-')) {
              // for USA we only need to validate the first part only
              return !isZipInState(Number.parseInt(firstPart), state)
            }

            return !isZipInState(Number.parseInt(combinedInput), state)
          }
          //No state inputted, invalid
          return true
        },
        extendedValidationErrorI18nKey: silentValidate
          ? ''
          : 'ERROR_ZIP_NOT_MATCH_STATE',
        setStateAction: setPostalCodeValidated,
      })
    }
  }

  /**
   * Validates if address line 1 is empty, and if the number of characters is
   * more or equal to 3 characters
   */
  const validateStreetAddressLineOne = (streetAddress?: string) =>
    validateInputWithError({
      input: streetAddress,
      emptyInputErrorI18nKey: 'ERROR_EMPTY_INPUT',
      extendedValidator: (streetAddress: string) =>
        streetAddress.length < addressMinLength,
      extendedValidationErrorI18nKey: 'STREET_ADDRESS_MIN',
      setStateAction: setStreetAddressLineOneValidated,
    })

  /**
   * Validates 2nd line of address, which is optional
   */
  const validateStreetAddressLineTwo = () => {
    setStreetAddressLineTwoValidated({
      valid: true,
    })
  }

  /**
   * Validates USA state against a ZIP code. `silentValidate`, does not render
   * an error message in order to avoid both USA State field and Postal code
   * field being in error state at the same time
   */
  const validateCountryState = (
    usaState?: StateData | string,
    postalCode?: string,
    silentValidate?: boolean
  ) => {
    if (typeof usaState === 'string') {
      validateInputWithError({
        input: usaState,
        emptyInputErrorI18nKey: 'ERROR_EMPTY_INPUT',
        setStateAction: _setUsaStateValidated,
      })
    } else {
      validateInputWithError({
        input: usaState?.iso_code,
        emptyInputErrorI18nKey: 'ERROR_SELECT_STATE',
        validateFormat: () =>
          !isZipInState(Number.parseInt(postalCode ?? ''), usaState),
        invalidInputErrorI18nKey: silentValidate
          ? ''
          : 'ERROR_STATE_NOT_MATCH_ZIP',
        setStateAction: _setUsaStateValidated,
      })
    }
  }

  /**
   * Validates a city where only alphabetic characters, hyphens, and spaces are
   * allowed
   */
  const validateCity = (city?: string) => {
    validateInputWithError({
      input: city,
      emptyInputErrorI18nKey: 'ERROR_EMPTY_INPUT',
      validateFormat: (city: string) => !regex.usaCity.test(city),
      invalidInputErrorI18nKey: 'ERROR_INVALID_CHARACTER',
      extendedValidator: (city: string) => city.length < cityLength,
      extendedValidationErrorI18nKey: 'ERROR_MIN_CITY_LENGTH',
      setStateAction: setCityValidated,
    })
  }

  /**
   * Validates if postal code is empty or not and if the inputted postal is in the
   * passed in state postal code range
   */
  const validatePostalCode = (
    postalCode?: string,
    state?: StateData,
    silentValidate?: boolean,
    postalCodeRegex?: RegExp,
    postalCodeFormatter?: typeof formatPostalCode,
    postalCodeLength?: [number, number]
  ) =>
    validateUserPostalCode(
      postalCode,
      state,
      silentValidate,
      postalCodeRegex,
      postalCodeFormatter,
      postalCodeLength
    )

  return {
    streetAddressLineOneValidated,
    validateStreetAddressLineOne,
    streetAddressLineTwoValidated,
    validateStreetAddressLineTwo,
    cityValidated,
    validateCity,
    postalCodeValidated,
    validatePostalCode,
    usaStateValidated,
    validateCountryState,
    allAddressFieldsValid:
      streetAddressLineOneValidated?.valid &&
      cityValidated?.valid &&
      postalCodeValidated?.valid &&
      usaStateValidated?.valid,
  }
}
