const API_OK = 200
// 3 mins request timeout
const REQUEST_TIMEOUT_IN_MS = 180_000
const withCredentials = false

/**
 * @param {string} api
 * @param {AbortSignal=} signal
 *
 * Fetches a scan session token which is necessary for a facescan
 *
 * @returns {Promise<string | undefined>}
 */
const getScanSessionToken = (api, signal) => {
  return new Promise((resolve, reject) => {
    if (!api) {
      return reject(new Error(`No api provided`))
    }

    const xhr = new XMLHttpRequest()
    xhr.open('GET', api, true)
    xhr.timeout = REQUEST_TIMEOUT_IN_MS

    xhr.onreadystatechange = () => {
      if (xhr.readyState === XMLHttpRequest.DONE) {
        if (xhr.status === API_OK) {
          try {
            const data = JSON.parse(xhr.responseText)
            resolve(data)
          } catch (error) {
            logClientError(error)
            reject(error)
          }
        } else {
          reject(new Error(`Request failed with status ${xhr.status}`))
        }
      }
    }

    xhr.onerror = () => reject(new Error('Network error'))
    xhr.ontimeout = () => reject(new Error('Request timed out'))

    xhr.send()
  })
}

/**
 * @typedef {{api:string, scanResultBody:object, email:string}} AuthenticateParams
 *
 * @param {AuthenticateParams}
 *
 * Authenticates the user by doing a facescan. The scan result is sent to the
 * passed in API in exchange for an auth token
 *
 * @returns {Promise<{scan_result_blob:string,
 * user_account_info:object, auth_token_info:AuthTokenInfo}>}
 */
const authenticateWithFace = ({ api, scanResultBody, email }) => {
  return new Promise((resolve, reject) => {
    if (!scanResultBody) {
      return reject(new Error('FATAL: No scan result body from face scan'))
    }

    if (!api || !email) {
      return reject(
        new TypeError(
          `No API or email for auth provided got >>${api}<<  >>${email}<<`
        )
      )
    }

    const xhr = new XMLHttpRequest()
    xhr.open('POST', api, true)
    xhr.setRequestHeader('Content-Type', 'application/json')
    xhr.withCredentials = withCredentials
    xhr.timeout = REQUEST_TIMEOUT_IN_MS

    xhr.onreadystatechange = () => {
      if (xhr.readyState === XMLHttpRequest.DONE) {
        if (xhr.status === API_OK) {
          try {
            const data = JSON.parse(xhr.responseText)
            resolve(data)
          } catch (error) {
            logClientError(error)
            reject(error)
          }
        } else {
          reject(new Error(`Request failed with status ${xhr.status}`))
        }
      }
    }

    xhr.onerror = () => reject(new Error('Network error'))
    xhr.ontimeout = () => reject(new Error('Request timed out'))

    xhr.send(
      JSON.stringify({
        scan_result: scanResultBody,
        email,
      })
    )
  })
}

/**
 * @typedef {{api:string, scanResultBody:object, email:string,
 * mockResponse?:boolean, authToken:string, enrollToken?:string}} EnrollParams
 * @typedef {{authToken:string, permissions:string, refreshToken:string,
 * remainingTime:number}} AuthTokenInfo
 *
 * @param {EnrollParams}
 *
 * Enrolls the user by doing a facescan. The scan result is sent to the
 * passed in API
 *
 * @returns {Promise<{scan_result_blob:string,enrollment_complete:boolean,
 * auth_token_info: AuthTokenInfo, user_account_info:object,face_scan_reference_id:string}>}
 */
const enrollFaceToFaceAuth = ({
  api,
  scanResultBody,
  email,
  mockResponse = false,
  authToken,
  enrollToken
}) => {
  return new Promise((resolve, reject) => {
    if (!authToken && !enrollToken) {
      return reject(new TypeError('No authToken or enrollToken, not provided, cannot start scan'))
    }


    if (!api && !email) {
      return reject(
        new TypeError(
          `No API or email or auth token provided`
        )
      )
    }

    if (!scanResultBody) {
      return reject(new Error('FATAL: No scan result body from face scan'))
    }

    const xhr = new XMLHttpRequest()
    xhr.open('POST', api, true)
    xhr.setRequestHeader('Content-Type', 'application/json')
    if (authToken) {
      xhr.setRequestHeader('X-Auth-Token', authToken)
    }
    if (enrollToken) {
      xhr.setRequestHeader('X-Enroll-Token', enrollToken)
    }
    xhr.withCredentials = withCredentials
    xhr.timeout = REQUEST_TIMEOUT_IN_MS

    xhr.onreadystatechange = () => {
      if (xhr.readyState === XMLHttpRequest.DONE) {
        if (xhr.status === API_OK) {
          try {
            const data = JSON.parse(xhr.responseText)
            resolve(data)
          } catch (error) {
            logClientError(error)
            reject(error)
          }
        } else {
          reject(new Error(`Request failed with status ${xhr.status}`))
        }
      }
    }

    xhr.onerror = () => reject(new Error('Network error'))
    xhr.ontimeout = () => reject(new Error('Request timed out'))

    xhr.send(
      JSON.stringify({
        mock: mockResponse,
        scanResult: scanResultBody,
        email,
      })
    )
  })
}

/**
 * @typedef {{api:string, idScanResultBody:object, faceScanRefId:string, authToken:string}} IDScanParams
 * @param {IDScanParams}
 *
 * Matches the user's scanned face with their ID.
 *
 * @returns {Promise<{scan_result_blob:string, completed:boolean}>}
 */
const scanId = ({ api, idScanResultBody, faceScanRefId, authToken }) => {
  return new Promise((resolve, reject) => {
    if (!api) {
      return reject(
        new TypeError(`No API or email for auth provided got >>${api}<< `)
      )
    }

    if (!authToken) {
      return reject(
        new TypeError('No auth token provided for ID match to face scan')
      )
    }

    if (!idScanResultBody) {
      return reject(new Error('FATAL: No scan result body from face scan'))
    }

    if (!faceScanRefId) {
      return reject(new Error('No face scan ref id, something went wrong'))
    }

    const xhr = new XMLHttpRequest()
    xhr.open('POST', api, true)
    xhr.setRequestHeader('Content-Type', 'application/json')
    xhr.setRequestHeader('X-Auth-Token', authToken)
    xhr.withCredentials = withCredentials
    xhr.timeout = REQUEST_TIMEOUT_IN_MS

    xhr.onreadystatechange = () => {
      if (xhr.readyState === XMLHttpRequest.DONE) {
        if (xhr.status === API_OK) {
          try {
            const data = JSON.parse(xhr.responseText)
            resolve(data)
          } catch (error) {
            logClientError(error)
            reject(error)
          }
        } else {
          reject(new Error(`Request failed with status ${xhr.status}`))
        }
      }
    }

    xhr.onerror = () => reject(new Error('Network error'))
    xhr.ontimeout = () => reject(new Error('Request timed out'))

    xhr.send(
      JSON.stringify({
        scan_result: idScanResultBody,
        face_scan_reference_id: faceScanRefId,
      })
    )
  })
}

const logClientError = (error) => {
  if (!error?.response) {
    console.error('Client error:', error)
  }
}

export {
  getScanSessionToken,
  authenticateWithFace,
  enrollFaceToFaceAuth,
  scanId,
}
