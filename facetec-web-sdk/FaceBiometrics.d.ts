type ScanType = 'ENROLLMENT' | 'AUTHENTICATION' | 'PHOTO_ID_SCAN'

type SDKConfig = {
  PublicFaceScanEncryptionKey: string
  deviceKeyIdentifier: string
  production: boolean
  /**
   * An JS Object containing strings from FaceTech or custom string
   */
  languageObject?:object
}

type FaceScanData = {
  permissions: string
  auth_token: string
  error: object | null
  enrollmentCompleted: boolean
  idScanCompleted: boolean
  user_account_info: object
}

/**
 * Face scan biometric operations
 */
export declare const FaceScanBiometrics: {
  /**
   * Initializes the SDK with the provided configuration values from the passed
   * in config object.
   */
  initializeSDK: (
    config: SDKConfig,
    onSDKinit: (isInit: boolean) => void
  ) => void
  /**
   * @note **Only call this function if the face scan SDK has been initialized
   * successfully!**
   *
   * Starts an FaceScan by firstly checking if a valid scan type has been
   * provided then a fetch is initiated to obtain a scan session token, if the
   * session scan token is successfully obtained then, a scan is initiated by
   * instantiating the `FaceScanProcessor`
   */
  startScan: ({
    scanType,
    email,
    onFaceScanStarted,
    onEnrollmentOnlyDone,
    signal,
    baseUrl,
    endpoint,
    onComplete,
    authToken,
  }: {
    scanType: ScanType
    email: string
    baseUrl: URL
    authToken?: string
    enrollToken?: string
    endpoint?: {
      sessionToken: string
      faceAuth: string
      faceEnroll: string
      idScan: string
    }
    /**
     * Issued when the SDK completes the scan, the scan result does not matter
     * it can be successful or not
     */
    onComplete: (data: FaceScanData) => void
    /**
     * Issued only if the enrollment has been completed, this is only used with
     * the PHOTO_ID_SCAN
     */
    onEnrollmentOnlyDone: (data: FaceScanData) => void
    signal?: AbortSignal
    /**
     * Issued when the the session token is successfully obtained
     */
    onFaceScanStarted?: () => void
  }) => void
  /**
   * Different scan types
   */
  scanType: ScanType
}
