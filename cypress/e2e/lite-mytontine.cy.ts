import { API } from '../../src/common/api/API'
import { IncomeForecastRequestBody } from '../../src/common/types/CommonTypes.types'
import { COMMON_CONSTANTS } from '../../src/common/utils/consts'
import { TESTING_IDS, TestID } from '../support/ui-component-ids'
import { fillAllInputFieldsWithData } from '../support/utils'

/**
 * Calls a function specified `times`
 */
const clickButtonTimes = (times: number, functionToCall: () => void) => {
  Array.from({ length: times }).forEach(functionToCall)
}

describe('MTL: Public tontinator page', () => {
  beforeEach(() => {
    cy.visit('/')
  })

  it(`modified default forecast params retAgeP1: 66 and retAgeP2: 71 are present on compare plan screen and 
    plan 1 amount is same as plan 2
    `, () => {
    cy.getByDataID(TESTING_IDS.retirementAgeIncrementButton).click({
      force: true,
    })

    cy.getByDataID(TESTING_IDS.oneTimeIncrementButton).click({
      force: true,
    })

    cy.getByDataID(TESTING_IDS.openComparePlanButton).click({
      force: true,
    })

    // Same data test id in slider page as well
    cy.getByDataID(TESTING_IDS.retirementAgeSliderBox).should('have.text', 66)
    cy.getByDataID(TESTING_IDS.p2RetirementAgeSliderBox).should('have.text', 66)

    // Both one time amount sliders have the same amount from tontinator screen
    cy.getByDataID(TESTING_IDS.p2OneTimeRetirementSliderBox).then((element) => {
      cy.getByDataID(TESTING_IDS.oneTimeSliderBox).should(
        'have.text',
        element.text()
      )
    })

    cy.snapshot('MTL: Tontinator modified params')
  })

  it('should disable USA:retirement age slider if retAge == currAge and retAge is >=73', () => {
    cy.get('.param-modes > :nth-child(2) > .toggle-button__container').click()

    clickButtonTimes(8, () => {
      cy.getByDataID(TESTING_IDS.currentAgeIncrementButton).click({
        force: true,
      })
    })

    cy.getByDataID(TESTING_IDS.retirementSlider).should(
      'have.class',
      'sliderInput--disabled'
    )

    cy.snapshot('MTL: Disabled retirement slider on Tontinator page')

    cy.getByDataID(TESTING_IDS.openComparePlanButton).click({
      force: true,
    })

    cy.getByDataID(TESTING_IDS.retirementSlider).should(
      'have.class',
      'sliderInput--disabled'
    )

    cy.snapshot('MTL: Disabled retirement slider on compare plans page')
  })

  it('modifies the plan B values if there is only one investment strategy for a country', () => {
    cy.intercept(
      {
        url: API.forecastRules,
      },
      {
        fixture: 'forecast_rules.json',
      }
    ).as('tontinatorRules')

    cy.visit('/', {
      qs: {
        country: 'JPN',
      },
    })

    cy.wait('@tontinatorRules').then(() => {
      clickButtonTimes(5, () => {
        cy.getByDataID(TESTING_IDS.retirementAgeIncrementButton).click({
          force: true,
        })
      })
      cy.getByDataID(TESTING_IDS.openComparePlanButton).click({
        force: true,
      })
      // Same data test id in slider page as well
      cy.getByDataID(TESTING_IDS.retirementAgeSliderBox).should('have.text', 70)
      // It is in the range of increments, so it should be 71
      cy.getByDataID(TESTING_IDS.p2RetirementAgeSliderBox).should(
        'have.text',
        75
      )
    })
  })

  it('should fetch tontinator rules and render returns on chart legend', () => {
    cy.intercept({
      url: API.forecastRules,
    }).as('tontinatorRules')

    cy.intercept({
      url: API.tontinatorForecast,
    }).as('tontinator request')

    cy.wait('@tontinatorRules').then(({ response }) => {
      expect(response?.statusCode).equal(200)

      cy.wait('@tontinator request').then(() => {
        cy.get('.legend-item-line-tontine-text').should('not.be.empty')
        cy.get('.legend-item-bank-deposit-text').should('not.be.empty')

        cy.getByDataID(TESTING_IDS.openComparePlanButton).click({
          force: true,
        })

        cy.wait('@tontinator request').then(() => {
          cy.get('.legend-item-plan1-text').should('not.be.empty')
          cy.get('.legend-item-plan2-text').should('not.be.empty')
        })
      })
    })
  })

  it('ui params match the request body sent to /tontinator and common params match for both plans', () => {
    cy.intercept({
      url: API.tontinatorForecast,
    }).as('tontinator request')

    clickButtonTimes(5, () => {
      cy.getByDataID(TESTING_IDS.retirementAgeIncrementButton).click({
        force: true,
      })
    })

    cy.getByDataID(TESTING_IDS.openComparePlanButton).click({
      force: true,
    })

    cy.wait('@tontinator request').then(
      ({
        request: { body },
      }: {
        request: { body: Array<IncomeForecastRequestBody> }
      }) => {
        const plan1Params = body[0]
        const plan2Params = body[1]

        const contributionParamsP1 = plan1Params.contributions

        const contributionParamsP2 = plan2Params.contributions

        cy.getByDataID(TESTING_IDS.retirementAgeSliderBox).should(
          'have.text',
          contributionParamsP1.payout_age.age
        )
        // It is in the range of increments, so it should be 71
        cy.getByDataID(TESTING_IDS.p2RetirementAgeSliderBox).should(
          'have.text',
          contributionParamsP2.payout_age.age
        )

        // Common params for both plans NEED TO MATCH!
        expect(plan1Params.demographic_data_current_age?.age).to.be.equal(
          plan2Params.demographic_data_current_age?.age
        )
        expect(plan1Params.demographic_data_sex).to.be.equal(
          plan2Params.demographic_data_sex
        )
        expect(plan1Params.demographic_data_country_of_residence).to.be.equal(
          plan2Params.demographic_data_country_of_residence
        )

        cy.getByDataID(TESTING_IDS.backBtnDesktop).click({ force: true })
        cy.getByDataID(`${TESTING_IDS.selectValueBtn}Female` as TestID).click({
          force: true,
        })
        cy.getByDataID(TESTING_IDS.currentAgeIncrementButton).click({
          force: true,
        })

        cy.getByDataID(TESTING_IDS.openComparePlanButton).click({
          force: true,
        })

        cy.wait('@tontinator request').then(
          ({
            request: { body },
          }: {
            request: { body: Array<IncomeForecastRequestBody> }
          }) => {
            const plan1Params = body[0]
            const plan2Params = body[1]

            // Common params for both plans NEED TO MATCH!
            expect(plan1Params.demographic_data_current_age?.age).to.be.equal(
              plan2Params.demographic_data_current_age?.age
            )
            expect(plan1Params.demographic_data_sex).to.be.equal(
              plan2Params.demographic_data_sex
            )
            expect(
              plan1Params.demographic_data_country_of_residence
            ).to.be.equal(plan2Params.demographic_data_country_of_residence)
          }
        )
      }
    )
  })

  it('choosing p1 data retAge: 68 and oneTime: $150,000 is present on first screen', () => {
    cy.intercept({
      url: API.tontinatorForecast,
    }).as('tontinator request')

    clickButtonTimes(2, () => {
      cy.getByDataID(TESTING_IDS.retirementAgeIncrementButton).click({
        force: true,
      })
    })
    cy.getByDataID(TESTING_IDS.openComparePlanButton).click({
      force: true,
    })
    cy.getByDataID(TESTING_IDS.retirementAgeIncrementButton).click({
      force: true,
      waitForAnimations: true,
    })

    cy.getByDataID(TESTING_IDS.oneTimeIncrementButton).click({ force: true })
    cy.getByDataID(TESTING_IDS.pickPlan1Button).click({ force: true })

    cy.getByDataID(TESTING_IDS.retirementAgeSliderBox).should('have.text', 68)
    cy.getByDataID(TESTING_IDS.oneTimeSliderBox).should('have.text', `$150,000`)
  })

  it('choosing p2 data retAge: 70 and oneTime: $150,000 is present on first screen', () => {
    cy.getByDataID(TESTING_IDS.openComparePlanButton).click({
      force: true,
    })
    cy.getByDataID(TESTING_IDS.p2OneTimeIncrementButton).click({ force: true })

    cy.getByDataID(TESTING_IDS.pickPlan2Button).click({ force: true })

    cy.getByDataID(TESTING_IDS.retirementAgeSliderBox).should('have.text', 65)
    cy.getByDataID(TESTING_IDS.oneTimeSliderBox).should('have.text', `$150,000`)
  })

  it('opens login modal and successfully sends a login email', () => {
    cy.intercept({
      url: 'http://localhost:8082/user_account/resend_verify',
    }).as('send login email')

    cy.getByDataID(TESTING_IDS.loginBtnDesktop).click({ force: true })

    cy.snapshot('MTL: Login modal empty')

    cy.getByDataID(TESTING_IDS.emailInput).type('<EMAIL>')
    cy.getByDataID(TESTING_IDS.signUpHomeBtn)
      .should('be.enabled')
      .click({ force: true })

    cy.wait('@send login email').then(({ response }) => {
      expect(response?.statusCode).to.equal(200)
      cy.get('.confirmationModal').should('be.visible')
    })
  })

  it('user signs up with "web_signup:true" successfully with modified params', () => {
    const testData = {
      sex: 'Female',
      currentAge: '65',
      retirementAge: '66',
      oneTimeAmount: 150_000,
    }

    cy.getByDataID(`${TESTING_IDS.selectValueBtn}Female` as TestID).click({
      force: true,
    })
    cy.getByDataID(TESTING_IDS.retirementAgeIncrementButton).click({
      force: true,
    })
    cy.getByDataID(TESTING_IDS.oneTimeIncrementButton).click({ force: true })

    // Using the variables from MyTontine config, does not work with
    // interceptors here for some reason
    cy.intercept({
      url: 'http://localhost:8082/user_account/create',
    }).as('liteSignUp')

    cy.getByDataID(TESTING_IDS.registerButton).click({ force: true })
    fillAllInputFieldsWithData({})
    cy.getByDataID(TESTING_IDS.registerButton).should('be.enabled')
    cy.getByDataID(TESTING_IDS.registerButton).click({ force: true })

    cy.wait('@liteSignUp').then(({ request, response }) => {
      const { body } = request as {
        body: {
          web_signup: boolean
          forecast_params: IncomeForecastRequestBody
        }
      }

      cy.log(`Sent to /create`, JSON.stringify(body, null, 2))
      expect(response?.statusCode).to.equal(200)

      expect(body).to.haveOwnProperty('web_signup').equal(true)
      expect(body?.forecast_params?.demographic_data_sex).to.equal(testData.sex)

      expect(
        body?.forecast_params?.demographic_data_current_age?.age.toString()
      ).to.contain(testData.currentAge)
      // We use because it is in age-month format, month is dynamic, would have
      // to freeze this test in time otherwise, but that is out of scope for
      // this test
      expect(
        body?.forecast_params?.contributions?.payout_age?.age.toString()
      ).to.contain(testData.retirementAge)

      expect(body?.forecast_params?.contributions?.onetime_amount).to.equal(
        testData.oneTimeAmount
      )
    })
  })
})

describe('MTL: Authenticated user', () => {
  beforeEach(() => {
    cy.intercept(
      {
        url: API.liteAuth,
      },
      {
        statusCode: 200,
        fixture: 'lite-login.json',
      }
    ).as('login')

    cy.visit(
      `/verify/?${COMMON_CONSTANTS.TONTINE_WEBSITE_ORIGIN}=http://localhost:3000&ver=token`
    )
  })

  it('renders mini referral layout and inits UI from plan', () => {
    cy.intercept({
      url: API.forecastRules,
    }).as('tontinatorRules')

    cy.wait('@tontinatorRules').then(() => {
      cy.wait('@login').then(({ response }) => {
        // Tontinator UI assertion
        cy.getByDataID(TESTING_IDS.currentAgeSlider).should('have.text', 68)
        cy.getByDataID(TESTING_IDS.retirementAgeSliderBox).should(
          'have.text',
          70
        )
        cy.getByDataID(TESTING_IDS.oneTimeSliderBox).should(
          'have.text',
          `$150,000`
        )
        cy.getByDataID(TESTING_IDS.updateLitePlanBtn).scrollIntoView()

        cy.getByDataID(TESTING_IDS.updateLitePlanBtn).should('be.disabled')
        // Mini layout assertion
        cy.get('.referral-code__text').should(
          'contain',
          response?.body?.referral_info?.[0].referral_code
        )
      })
    })
  })

  it('updates user plan successfully with new UI values', () => {
    // intercept plan change here
    cy.intercept(
      {
        url: API.updateLitePlan,
      },
      {
        statusCode: 200,
      }
    ).as('updatePlan')

    cy.wait('@login').then(() => {
      // Update UI
      cy.getByDataID(`${TESTING_IDS.selectValueBtn}Female` as TestID).click({
        force: true,
        multiple: true,
      })
      cy.getByDataID(TESTING_IDS.retirementAgeIncrementButton).click({
        force: true,
        multiple: true,
      })
      cy.getByDataID(TESTING_IDS.currentAgeIncrementButton).click({
        force: true,
        multiple: true,
      })

      // Save plan
      cy.getByDataID(TESTING_IDS.updateLitePlanBtn).should('be.enabled')
      cy.getByDataID(TESTING_IDS.updateLitePlanBtn).click({ force: true })

      cy.wait('@updatePlan').then(({ response, request }) => {
        cy.log(JSON.stringify(request.body))
        expect(response?.statusCode).to.equal(200)

        cy.getByDataID(TESTING_IDS.currentAgeSlider).should(
          'have.text',
          request?.body?.contributionAge?.age.toString()
        )
        cy.getByDataID(TESTING_IDS.retirementAgeSliderBox).should(
          'have.text',
          request?.body?.retirementAge?.age.toString()
        )

        cy.getByDataID(TESTING_IDS.updateLitePlanBtn).should('be.disabled')
      })
    })
  })
})

describe('MTL: Rewards page', () => {
  beforeEach(() => {
    cy.intercept({
      url: `${API.getAgreement}/TermsAndConditions/latest`,
    }).as('agreement')

    cy.visit('/rewards')

    cy.wait('@agreement')
  })

  it('visits rewards page and sends login email successful', () => {
    cy.intercept({
      url: 'http://localhost:8082/user_account/resend_verify',
    }).as('send login email')

    cy.snapshot('MTL: Rewards page no-auth')

    cy.getByDataID(TESTING_IDS.emailInput).type('<EMAIL>')
    cy.getByDataID(TESTING_IDS.signUpHomeBtn)
      .should('be.enabled')
      .click({ force: true })

    cy.wait('@send login email').then(({ response }) => {
      expect(response?.statusCode).to.equal(200)
      cy.get('.confirmationModal').should('be.visible')
    })
  })
})
