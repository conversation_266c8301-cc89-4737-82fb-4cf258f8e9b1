#!/usr/bin/env bash

# This script is meant to be used for CI, do not run locally or it will interfere with
# your docker repo

set -euxo pipefail

DIR="$(dirname $(readlink -f $0))"
UAS_PORT=8082
VITE_HOST="0.0.0.0"
FULL_VERSION_PORT=8080
LITE_VERSION_PORT=9000
export VITE_ROBO_BACKEND_URL="http://localhost:$UAS_PORT"
# Enough time to target if vite takes 33s to start
VITE_SLEEP=15

cd_webapp() {
  cd "$DIR/.."
}
cd_ra() {
  cd "$DIR/../robo-actuary-docker-compose"
}

# Stops all background processes
cleanup() {
  echo "Killing frontend server/s"

  # Change into frontend directory.
  cd_webapp
  # Kill the vite server running on 8080
  fuser -k $FULL_VERSION_PORT/tcp || true
  # Kill the vite server running on 8080
  fuser -k $LITE_VERSION_PORT/tcp || true

  echo "Killing docker and removing images"
  cd_ra
  docker compose down
  docker compose rm -f

  cd_webapp
  # Remove the cloned submodule
  rm -r robo-actuary-docker-compose

  echo "Done clean up"
}

# Util function for logging
wait_log() {
  # What is being waited for
  local service_name="$1"

  # Check if the argument is provided
  if [[ -z "$service_name" ]]; then
    echo "WAITING: No service name provided."
  fi

  echo -e "\n WAITING: $service_name \n"
}

# Waits for a URL to come online
wait_for_service() {
  # What is being waited for
  local url_name="$1"
  local wait_time="$2"
  local service_name="$3"

  while [[ "$(curl -s -o /dev/null -w ''%{http_code}'' $url_name)" != "200" ]]; do
    wait_log "$service_name"
    sleep $wait_time
  done
}

start_lite_testing() {
  # Build lite version of webapp
  ./scripts/dev-server.sh -e development -m lite -i true -p "$LITE_VERSION_PORT" -h "$VITE_HOST" &
  # Wait until the User Account Service is ready.
  wait_for_service "localhost:$UAS_PORT/health_check" "1" "Backend services to come online"
  # Wait until vite is ready.
  wait_for_service "$VITE_HOST:$LITE_VERSION_PORT" "$VITE_SLEEP" "Vite LITE build to finish building"

  #Start e2e testing for lite version
  npm run cypress-headless:l
  # Kill the vite server for MTL
  fuser -k $LITE_VERSION_PORT/tcp || true
}

start_full_version_testing() {
  # Build the full version of webapp
  npm run tc-report && ./scripts/dev-server.sh -e development -i true -p "$FULL_VERSION_PORT" -h "$VITE_HOST" &
  # Wait until vite is ready.
  wait_for_service "$VITE_HOST:$FULL_VERSION_PORT" "$VITE_SLEEP" "Vite FULL version to finish building"
  # Start e2e testing for full version
  npm run cypress-headless
}

clone_docker_repo_and_start_docker() {
  # Clone the facetec-web-sdk repo
  ./scripts/prepare.sh
  <NAME_EMAIL>:TontineTrust/robo-actuary-docker-compose.git || true
  cd robo-actuary-docker-compose
  git reset --hard HEAD

  # Cd into the docker repo and pull images
  cd_ra
  ./run.sh >/dev/null 2>&1 &

  # Start all the services
  # >/dev/null 2>&1 discards backend logs so it does not spam the console
  docker compose up >/dev/null 2>&1 &
}

#  >>>> EXECUTION STARTS FROM HERE <<<<
# Runs clean up when everything if finished
trap 'cleanup &' EXIT

clone_docker_repo_and_start_docker

# Go to webapp and start the testing process
cd_webapp

start_lite_testing
start_full_version_testing
