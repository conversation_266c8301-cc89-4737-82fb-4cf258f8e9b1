import task from '@cypress/code-coverage/task'
import { defineConfig } from 'cypress'
import vitePreprocessor from 'cypress-vite'
import path, { dirname } from 'path'
import { fileURLToPath } from 'url'

const __filename = fileURLToPath(import.meta.url)
const __dirname = dirname(__filename)

// Webapp dev server port
const port = process.env.VITE_PORT ?? 8080

export default defineConfig({
  video: false,
  screenshotOnRunFailure: false,

  component: {
    // Gotta be separate setup e2e and compo, otherwise does not work
    // I have no idea why
    setupNodeEvents(on, config) {
      task(on, config)
      return config
    },
    devServer: {
      framework: 'react',
      bundler: 'vite',
    },
  },

  e2e: {
    baseUrl: `http://0.0.0.0:${port}`,
    setupNodeEvents(on, config) {
      // Apply preprocessor only for e2e tests
      if (config.testingType === 'e2e') {
        on(
          'file:preprocessor',
          vitePreprocessor({
            configFile: path.resolve(__dirname, '../vite.config.ts'),
          })
        )
      }
      task(on, config)
      return config
    },
  },
})
